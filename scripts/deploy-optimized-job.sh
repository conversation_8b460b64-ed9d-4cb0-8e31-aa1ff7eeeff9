#!/bin/bash

# 优化的单作业部署脚本
# 适用于资源有限的环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
NAMESPACE="olap"
JOB_NAME="olap-flink-paimon-optimized-job"
KUBECTL_CMD="kubectl"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 kubectl 连接
check_kubectl() {
    log_info "检查 kubectl 连接..."
    if ! $KUBECTL_CMD cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    log_success "kubectl 连接正常"
}

# 检查命名空间
check_namespace() {
    log_info "检查命名空间 $NAMESPACE..."
    if ! $KUBECTL_CMD get namespace $NAMESPACE &> /dev/null; then
        log_warning "命名空间 $NAMESPACE 不存在，正在创建..."
        $KUBECTL_CMD create namespace $NAMESPACE
    fi
    log_success "命名空间 $NAMESPACE 准备就绪"
}

# 停止现有作业
stop_existing_job() {
    log_info "停止现有的 Flink 作业..."
    
    # 停止原来的作业
    if $KUBECTL_CMD get flinkdeployment olap-flink-paimon-job -n $NAMESPACE &> /dev/null; then
        log_warning "停止原有作业..."
        $KUBECTL_CMD delete flinkdeployment olap-flink-paimon-job -n $NAMESPACE --force --grace-period=0 || true
    fi
    
    # 停止优化作业
    if $KUBECTL_CMD get flinkdeployment $JOB_NAME -n $NAMESPACE &> /dev/null; then
        log_warning "停止优化作业..."
        $KUBECTL_CMD delete flinkdeployment $JOB_NAME -n $NAMESPACE --force --grace-period=0 || true
    fi
    
    log_info "等待作业完全停止..."
    sleep 30
}

# 部署优化作业
deploy_optimized_job() {
    local deployment_file="helm-charts/paimon/${JOB_NAME}-deployment.yaml"
    
    log_info "部署优化的 Flink 作业..."
    
    if [ ! -f "$deployment_file" ]; then
        log_error "部署文件不存在: $deployment_file"
        return 1
    fi
    
    # 应用部署配置
    $KUBECTL_CMD apply -f "$deployment_file"
    
    # 等待作业启动
    log_info "等待作业启动..."
    timeout=600  # 10分钟超时
    elapsed=0
    
    while [ $elapsed -lt $timeout ]; do
        local status=$($KUBECTL_CMD get flinkdeployment $JOB_NAME -n $NAMESPACE -o jsonpath='{.status.jobStatus.state}' 2>/dev/null || echo "UNKNOWN")
        
        if [ "$status" = "RUNNING" ]; then
            log_success "作业启动成功"
            return 0
        elif [ "$status" = "FAILED" ]; then
            log_error "作业启动失败"
            show_job_logs
            return 1
        fi
        
        echo -n "."
        sleep 10
        elapsed=$((elapsed + 10))
    done
    
    echo ""
    log_error "作业启动超时"
    show_job_logs
    return 1
}

# 检查作业状态
check_job_status() {
    log_info "检查作业状态..."
    
    # 获取作业状态
    local status=$($KUBECTL_CMD get flinkdeployment $JOB_NAME -n $NAMESPACE -o jsonpath='{.status.jobStatus.state}' 2>/dev/null || echo "NOT_FOUND")
    local lifecycle=$($KUBECTL_CMD get flinkdeployment $JOB_NAME -n $NAMESPACE -o jsonpath='{.status.lifecycleState}' 2>/dev/null || echo "NOT_FOUND")
    
    echo "  作业状态: $status"
    echo "  生命周期: $lifecycle"
    
    if [ "$status" = "RUNNING" ]; then
        log_success "作业运行正常"
        
        # 显示资源使用情况
        show_resource_usage
        return 0
    else
        log_warning "作业状态异常: $status"
        return 1
    fi
}

# 显示资源使用情况
show_resource_usage() {
    log_info "显示资源使用情况..."
    
    # 获取 Pod 信息
    local pods=$($KUBECTL_CMD get pods -n $NAMESPACE -l app=$JOB_NAME -o jsonpath='{.items[*].metadata.name}' 2>/dev/null || echo "")
    
    if [ -n "$pods" ]; then
        echo "=== 资源使用情况 ==="
        for pod in $pods; do
            echo "Pod: $pod"
            $KUBECTL_CMD top pod $pod -n $NAMESPACE 2>/dev/null || echo "  无法获取资源使用情况"
        done
    else
        log_warning "未找到运行中的 Pod"
    fi
}

# 显示作业日志
show_job_logs() {
    log_info "显示作业日志 (最近100行)..."
    
    # 获取 JobManager Pod
    local jm_pod=$($KUBECTL_CMD get pods -n $NAMESPACE -l app=$JOB_NAME,component=jobmanager -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -n "$jm_pod" ]; then
        echo "=== JobManager 日志 ==="
        $KUBECTL_CMD logs $jm_pod -n $NAMESPACE --tail=100 || true
    else
        log_warning "未找到 JobManager Pod"
    fi
    
    # 获取 TaskManager Pod
    local tm_pod=$($KUBECTL_CMD get pods -n $NAMESPACE -l app=$JOB_NAME,component=taskmanager -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -n "$tm_pod" ]; then
        echo "=== TaskManager 日志 ==="
        $KUBECTL_CMD logs $tm_pod -n $NAMESPACE --tail=50 || true
    else
        log_warning "未找到 TaskManager Pod"
    fi
}

# 监控作业运行
monitor_job() {
    log_info "开始监控作业运行状态..."
    
    while true; do
        echo ""
        echo "=== $(date) ==="
        
        if check_job_status; then
            log_info "作业运行正常，继续监控..."
        else
            log_error "作业状态异常，显示日志..."
            show_job_logs
            break
        fi
        
        sleep 60  # 每分钟检查一次
    done
}

# 主函数
main() {
    local action=${1:-"deploy"}
    
    log_info "开始执行优化作业操作: $action"
    
    # 检查前置条件
    check_kubectl
    check_namespace
    
    case $action in
        "deploy")
            log_info "执行部署操作..."
            stop_existing_job
            
            if deploy_optimized_job; then
                log_success "部署成功！"
                check_job_status
            else
                log_error "部署失败"
                exit 1
            fi
            ;;
            
        "status")
            check_job_status
            ;;
            
        "logs")
            show_job_logs
            ;;
            
        "monitor")
            monitor_job
            ;;
            
        "stop")
            stop_existing_job
            log_success "作业停止完成"
            ;;
            
        *)
            echo "用法: $0 [deploy|status|logs|monitor|stop]"
            echo ""
            echo "操作:"
            echo "  deploy  - 部署优化作业 (默认)"
            echo "  status  - 检查作业状态"
            echo "  logs    - 显示作业日志"
            echo "  monitor - 持续监控作业状态"
            echo "  stop    - 停止作业"
            echo ""
            echo "示例:"
            echo "  $0 deploy"
            echo "  $0 status"
            echo "  $0 monitor"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
