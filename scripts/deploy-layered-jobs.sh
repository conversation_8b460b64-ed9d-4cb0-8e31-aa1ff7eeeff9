#!/bin/bash

# 分层 Flink 作业部署脚本
# 用于部署优化后的多层 Flink 作业

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
NAMESPACE="olap"
KUBECTL_CMD="kubectl"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 kubectl 连接
check_kubectl() {
    log_info "检查 kubectl 连接..."
    if ! $KUBECTL_CMD cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    log_success "kubectl 连接正常"
}

# 检查命名空间
check_namespace() {
    log_info "检查命名空间 $NAMESPACE..."
    if ! $KUBECTL_CMD get namespace $NAMESPACE &> /dev/null; then
        log_warning "命名空间 $NAMESPACE 不存在，正在创建..."
        $KUBECTL_CMD create namespace $NAMESPACE
    fi
    log_success "命名空间 $NAMESPACE 准备就绪"
}

# 停止现有作业
stop_existing_jobs() {
    log_info "停止现有的 Flink 作业..."
    
    # 停止原来的单一作业
    if $KUBECTL_CMD get flinkdeployment olap-flink-paimon-job -n $NAMESPACE &> /dev/null; then
        log_warning "停止原有的单一作业..."
        $KUBECTL_CMD delete flinkdeployment olap-flink-paimon-job -n $NAMESPACE --force --grace-period=0 || true
    fi
    
    # 停止分层作业
    for layer in foundation wide aggregation; do
        job_name="olap-flink-paimon-${layer}-job"
        if $KUBECTL_CMD get flinkdeployment $job_name -n $NAMESPACE &> /dev/null; then
            log_warning "停止 $layer 层作业..."
            $KUBECTL_CMD delete flinkdeployment $job_name -n $NAMESPACE --force --grace-period=0 || true
        fi
    done
    
    log_info "等待作业完全停止..."
    sleep 30
}

# 部署单个层级的作业
deploy_layer() {
    local layer=$1
    local job_name="olap-flink-paimon-${layer}-job"
    local deployment_file="helm-charts/paimon/${job_name}-deployment.yaml"
    
    log_info "部署 $layer 层作业..."
    
    if [ ! -f "$deployment_file" ]; then
        log_error "部署文件不存在: $deployment_file"
        return 1
    fi
    
    # 应用部署配置
    $KUBECTL_CMD apply -f "$deployment_file"
    
    # 等待作业启动
    log_info "等待 $layer 层作业启动..."
    timeout=300  # 5分钟超时
    elapsed=0
    
    while [ $elapsed -lt $timeout ]; do
        if $KUBECTL_CMD get flinkdeployment $job_name -n $NAMESPACE -o jsonpath='{.status.jobStatus.state}' 2>/dev/null | grep -q "RUNNING"; then
            log_success "$layer 层作业启动成功"
            return 0
        fi
        
        sleep 10
        elapsed=$((elapsed + 10))
        echo -n "."
    done
    
    echo ""
    log_error "$layer 层作业启动超时"
    return 1
}

# 检查作业状态
check_job_status() {
    local layer=$1
    local job_name="olap-flink-paimon-${layer}-job"
    
    log_info "检查 $layer 层作业状态..."
    
    # 获取作业状态
    local status=$($KUBECTL_CMD get flinkdeployment $job_name -n $NAMESPACE -o jsonpath='{.status.jobStatus.state}' 2>/dev/null || echo "NOT_FOUND")
    local lifecycle=$($KUBECTL_CMD get flinkdeployment $job_name -n $NAMESPACE -o jsonpath='{.status.lifecycleState}' 2>/dev/null || echo "NOT_FOUND")
    
    echo "  状态: $status"
    echo "  生命周期: $lifecycle"
    
    if [ "$status" = "RUNNING" ]; then
        log_success "$layer 层作业运行正常"
        return 0
    else
        log_warning "$layer 层作业状态异常: $status"
        return 1
    fi
}

# 显示作业日志
show_job_logs() {
    local layer=$1
    local job_name="olap-flink-paimon-${layer}-job"
    
    log_info "显示 $layer 层作业日志 (最近50行)..."
    
    # 获取 JobManager Pod
    local jm_pod=$($KUBECTL_CMD get pods -n $NAMESPACE -l app=$job_name,component=jobmanager -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -n "$jm_pod" ]; then
        echo "=== $layer 层 JobManager 日志 ==="
        $KUBECTL_CMD logs $jm_pod -n $NAMESPACE --tail=50 || true
    else
        log_warning "未找到 $layer 层 JobManager Pod"
    fi
}

# 主部署流程
main() {
    log_info "开始部署优化后的分层 Flink 作业..."
    
    # 检查前置条件
    check_kubectl
    check_namespace
    
    # 解析命令行参数
    local action=${1:-"deploy"}
    local layers=${2:-"foundation,wide,aggregation"}
    
    case $action in
        "deploy")
            log_info "执行部署操作..."
            stop_existing_jobs
            
            # 按顺序部署各层
            IFS=',' read -ra LAYER_ARRAY <<< "$layers"
            for layer in "${LAYER_ARRAY[@]}"; do
                if deploy_layer "$layer"; then
                    log_success "$layer 层部署成功"
                else
                    log_error "$layer 层部署失败"
                    exit 1
                fi
                
                # 层间等待时间，确保依赖关系
                if [ "$layer" != "aggregation" ]; then
                    log_info "等待 $layer 层稳定运行..."
                    sleep 60
                fi
            done
            
            log_success "所有层级部署完成！"
            ;;
            
        "status")
            log_info "检查作业状态..."
            IFS=',' read -ra LAYER_ARRAY <<< "$layers"
            for layer in "${LAYER_ARRAY[@]}"; do
                check_job_status "$layer"
            done
            ;;
            
        "logs")
            log_info "显示作业日志..."
            IFS=',' read -ra LAYER_ARRAY <<< "$layers"
            for layer in "${LAYER_ARRAY[@]}"; do
                show_job_logs "$layer"
            done
            ;;
            
        "stop")
            log_info "停止作业..."
            stop_existing_jobs
            log_success "作业停止完成"
            ;;
            
        *)
            echo "用法: $0 [deploy|status|logs|stop] [layers]"
            echo ""
            echo "操作:"
            echo "  deploy  - 部署分层作业 (默认)"
            echo "  status  - 检查作业状态"
            echo "  logs    - 显示作业日志"
            echo "  stop    - 停止所有作业"
            echo ""
            echo "层级 (用逗号分隔):"
            echo "  foundation - 基础层 (Fact + Dimension)"
            echo "  wide       - 宽表层"
            echo "  aggregation - 聚合层"
            echo ""
            echo "示例:"
            echo "  $0 deploy foundation,wide"
            echo "  $0 status"
            echo "  $0 logs foundation"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
