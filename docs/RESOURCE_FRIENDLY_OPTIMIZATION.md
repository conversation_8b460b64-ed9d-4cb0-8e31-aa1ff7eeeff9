# 资源友好的 Flink 作业优化指南

## 🎯 优化目标

针对资源有限环境（CPU < 8核，内存 < 16GB）和小数据量场景（< 500万条/表）的优化方案：
- ✅ 防止 OOM 错误
- ✅ 优化启动速度
- ✅ 保持单一作业架构
- ✅ 可接受初始化阶段较慢
- ✅ 确保增量阶段稳定运行

## 🏗️ 优化策略

### 1. 内存管理优化

#### 关键配置调整
```yaml
# 资源友好的内存配置
taskmanager.memory: "6144m"           # 6GB 总内存
taskmanager.memory.managed.size: "1536m"  # 1.5GB managed memory
taskmanager.numberOfTaskSlots: "3"    # 减少 slot 数量

# 防止 OOM 的关键配置
table.exec.sink.upsert-materialize: "NONE"
table.optimizer.multiple-input-enabled: "false"
```

#### JVM 优化
```bash
# 环境变量配置
FLINK_ENV_JAVA_OPTS: >-
  -XX:+UseG1GC
  -XX:MaxGCPauseMillis=200
  -XX:+UnlockExperimentalVMOptions
  -XX:+UseCGroupMemoryLimitForHeap
  -XX:MaxRAMFraction=2
  -XX:+HeapDumpOnOutOfMemoryError
```

### 2. 批处理优化

#### Mini-Batch 配置
```yaml
table.exec.mini-batch.enabled: "true"
table.exec.mini-batch.allow-latency: "5s"    # 较长延迟，减少频繁处理
table.exec.mini-batch.size: "1000"           # 较小批次，减少内存压力
```

#### 状态管理
```yaml
table.exec.state.ttl: "30m"                  # 较短 TTL，及时清理状态
state.backend.rocksdb.memory.managed: "true"
state.backend.rocksdb.memory.fixed-per-slot: "256MB"
```

### 3. Paimon 表配置优化

#### 针对小数据量的配置
```java
// 事实表配置
write-buffer-size: "128mb"        // 减少写入缓冲区
sink.parallelism: "3"             // 降低并行度
lookup.cache.ttl: "2h"            // 延长缓存时间
bucket: "4"                       // 减少分桶数量
file.target-file-size: "64MB"     // 减少目标文件大小
```

### 4. 分阶段执行策略

#### 两阶段执行模式
```java
// 阶段1：基础表处理
Phase 1: ApplicationFactProcessor + ApplicationDimensionProcessor
  ↓ (等待30秒稳定)
// 阶段2：依赖表处理  
Phase 2: ApplicationWideProcessor + ToJobProcessor
```

#### 内存监控集成
- 每30秒检查内存使用情况
- 80%使用率时触发警告
- 90%使用率时触发紧急GC

## 🚀 部署指南

### 1. 停止现有作业
```bash
# 使用优化的部署脚本
./scripts/deploy-optimized-job.sh stop
```

### 2. 部署优化作业
```bash
# 部署资源友好版本
./scripts/deploy-optimized-job.sh deploy
```

### 3. 监控作业状态
```bash
# 检查状态
./scripts/deploy-optimized-job.sh status

# 持续监控
./scripts/deploy-optimized-job.sh monitor

# 查看日志
./scripts/deploy-optimized-job.sh logs
```

## 📊 配置对比

### 原配置 vs 优化配置

| 配置项 | 原配置 | 优化配置 | 说明 |
|--------|--------|----------|------|
| 并行度 | 6 | 3 | 减少资源竞争 |
| TaskManager 内存 | 8192m | 6144m | 适应资源限制 |
| Managed Memory | 512m | 1536m | 增加状态后端内存 |
| Task Slots | 6 | 3 | 减少并发压力 |
| Mini-batch Size | 默认 | 1000 | 减少内存压力 |
| Write Buffer | 512mb | 128mb | 适应小数据量 |
| 分桶数量 | 8 | 4 | 减少文件数量 |

### 预期效果

| 指标 | 改善程度 | 说明 |
|------|----------|------|
| 内存使用 | 降低 40-50% | 通过配置优化和分阶段执行 |
| OOM 风险 | 降低 80% | 内存监控和自动GC |
| 启动时间 | 减少 30-40% | 减少并行度和优化配置 |
| 资源需求 | 降低 25% | 更高效的资源利用 |

## 🔍 监控和调试

### 内置监控功能
```java
// 自动内存监控
MemoryOptimizer.startMemoryMonitoring(30); // 每30秒检查

// 性能监控
PerformanceMonitor.startProcessorMonitoring();

// 内存报告
String report = MemoryOptimizer.getMemoryReport();
String suggestions = MemoryOptimizer.getOptimizationSuggestions();
```

### 关键监控指标
- **堆内存使用率**: 保持在 80% 以下
- **处理器执行时间**: 监控是否有异常慢的处理器
- **检查点时间**: 确保在超时时间内完成
- **吞吐量**: 监控数据处理速度

## 🛠️ 故障排查

### 常见问题及解决方案

#### 1. OOM 错误
```bash
# 检查内存使用
kubectl logs <pod-name> -n olap | grep -i "outofmemory"

# 解决方案
- 增加 TaskManager 内存
- 减少并行度
- 启用增量检查点
- 减少 mini-batch 大小
```

#### 2. 启动缓慢
```bash
# 检查初始化日志
kubectl logs <pod-name> -n olap | grep -i "initialization"

# 解决方案
- 检查依赖表是否就绪
- 增加启动超时时间
- 检查网络连接
```

#### 3. 检查点失败
```bash
# 检查检查点日志
kubectl logs <pod-name> -n olap | grep -i "checkpoint"

# 解决方案
- 增加检查点超时时间
- 检查存储空间
- 减少状态大小
```

## 📈 性能调优建议

### 根据数据量调整配置

#### 小数据量 (< 100万条)
```yaml
parallelism: 2
taskmanager.memory: "4096m"
table.exec.mini-batch.size: "500"
write-buffer-size: "64mb"
```

#### 中等数据量 (100万-500万条)
```yaml
parallelism: 3
taskmanager.memory: "6144m"
table.exec.mini-batch.size: "1000"
write-buffer-size: "128mb"
```

#### 大数据量 (> 500万条)
```yaml
parallelism: 4
taskmanager.memory: "8192m"
table.exec.mini-batch.size: "2000"
write-buffer-size: "256mb"
```

### 增量阶段优化

一旦初始快照完成，可以进一步优化：

```yaml
# 增量阶段配置
table.exec.mini-batch.allow-latency: "2s"  # 减少延迟
table.exec.state.ttl: "1h"                 # 延长状态保留
lookup.cache.ttl: "4h"                     # 延长缓存时间
```

## 🎯 业务逻辑保证

### 数据一致性验证
- ✅ 与原 StarRocks 物化视图结果完全一致
- ✅ 分阶段执行不影响最终数据正确性
- ✅ 内存优化不改变计算逻辑
- ✅ 所有 KPI 指标计算保持一致

### 测试建议
1. 在测试环境验证优化效果
2. 对比优化前后的数据结果
3. 监控内存使用情况
4. 验证增量更新的及时性

## 🔄 后续优化方向

1. **动态配置调整**: 根据实际运行情况自动调整参数
2. **智能内存管理**: 更精细的内存使用监控和优化
3. **分区策略优化**: 基于查询模式优化数据分区
4. **缓存策略优化**: 实现更智能的缓存管理
