# Flink + Paimon 流处理作业优化指南

## 🎯 优化目标

将原来的单一重量级 Flink 作业优化为轻量级的分层架构，解决以下问题：
- ✅ 减少算子数量和复杂度
- ✅ 降低内存使用和 OOM 风险
- ✅ 提升作业启动速度
- ✅ 保持业务逻辑完全一致

## 🏗️ 架构优化

### 原架构问题
```
单一作业 (PaimonDwdJob)
├── ApplicationFactProcessor
├── ApplicationDimensionProcessor  
├── ApplicationWideProcessor
└── ToJobProcessor
```
**问题**: 所有处理器在同一个 StatementSet 中，创建复杂依赖图

### 优化后架构
```
分层架构
├── 基础层 (Foundation Layer)
│   ├── OptimizedApplicationFactProcessor
│   └── ApplicationDimensionProcessor
├── 宽表层 (Wide Layer)
│   └── OptimizedApplicationWideProcessor
└── 聚合层 (Aggregation Layer)
    ├── ToJobProcessor
    └── ToClientProcessor
```

## 📊 性能优化详情

### 1. 架构层面优化

#### 分层部署
- **基础层**: 处理事实表和维度表，并行度 4，内存 4GB
- **宽表层**: 处理复杂关联，并行度 6，内存 6GB  
- **聚合层**: 处理聚合计算，并行度 8，内存 8GB

#### 资源配置优化
```yaml
# 基础层配置
taskmanager.memory.managed.size: "1gb"
taskmanager.numberOfTaskSlots: "4"
table.exec.mini-batch.size: "2000"

# 宽表层配置  
taskmanager.memory.managed.size: "1.5gb"
taskmanager.numberOfTaskSlots: "6"
table.optimizer.join.broadcast-threshold: "20MB"

# 聚合层配置
taskmanager.memory.managed.size: "2gb" 
taskmanager.numberOfTaskSlots: "8"
table.exec.mini-batch.size: "5000"
```

### 2. SQL 查询优化

#### UNION ALL 优化
- 减少不必要的类型转换
- 预计算分区字段 `DATE_FORMAT(created_date, 'yyyy-MM')`
- 优化时态关联模式

#### 时态关联优化
```sql
-- 优化前
FROM table1 t1 JOIN table2 FOR SYSTEM_TIME AS OF PROCTIME() t2

-- 优化后  
FROM (
    SELECT *, PROCTIME() AS proc_time 
    FROM table1
) t1 
JOIN table2 FOR SYSTEM_TIME AS OF t1.proc_time t2
```

#### CTE 结构简化
- 减少嵌套层次
- 优化关联顺序（小表在右侧）
- 使用更高效的字段选择

### 3. Paimon 存储优化

#### 表类型特化配置
```java
// 事实表配置
TableType.FACT -> {
    write-buffer-size: "512mb"
    sink.parallelism: "4" 
    lookup.cache.ttl: "1h"
    bucket: "8"
}

// 维度表配置
TableType.DIMENSION -> {
    write-buffer-size: "128mb"
    lookup.cache.ttl: "24h"
    bucket: "4"
}

// 宽表配置
TableType.WIDE -> {
    write-buffer-size: "512mb"
    lookup.cache.ttl: "30m"
    scan.projection.enabled: "true"
}
```

#### 压缩和存储优化
- 使用 LZ4 压缩算法
- 优化文件大小：64MB-512MB
- 智能分桶策略：4-16 桶
- 预提交压缩：减少小文件

## 🚀 部署指南

### 1. 停止原有作业
```bash
kubectl delete flinkdeployment olap-flink-paimon-job -n olap --force --grace-period=0
```

### 2. 部署分层作业
```bash
# 使用部署脚本
./scripts/deploy-layered-jobs.sh deploy

# 或手动部署
kubectl apply -f helm-charts/paimon/olap-flink-paimon-foundation-job-deployment.yaml
kubectl apply -f helm-charts/paimon/olap-flink-paimon-wide-job-deployment.yaml  
kubectl apply -f helm-charts/paimon/olap-flink-paimon-aggregation-job-deployment.yaml
```

### 3. 监控作业状态
```bash
# 检查所有层级状态
./scripts/deploy-layered-jobs.sh status

# 查看日志
./scripts/deploy-layered-jobs.sh logs foundation
```

## 📈 性能监控

### 内置监控功能
- 处理器执行时间监控
- 内存使用情况跟踪
- 配置验证和建议
- 自动性能报告生成

### 监控指标
```java
// 性能指标示例
{
    "executionTimes": {
        "OptimizedApplicationFactProcessor": 8500,
        "ApplicationDimensionProcessor": 3200
    },
    "totalExecutionTime": 11700,
    "avgExecutionTime": 5850
}
```

## 🔧 配置调优建议

### 内存配置
```yaml
# 原配置问题
taskmanager.memory: "8192m"
taskmanager.memory.managed.size: "512m"  # 过小

# 优化后配置
taskmanager.memory: "4096m-8192m"  # 按层级调整
taskmanager.memory.managed.size: "1gb-2gb"  # 增加到合理比例
```

### 并行度配置
- **基础层**: 4 (平衡资源使用)
- **宽表层**: 6 (处理复杂关联)  
- **聚合层**: 8 (高吞吐量需求)

### 检查点配置
```yaml
execution.checkpointing.timeout: "1200s-1800s"  # 按层级调整
execution.checkpointing.incremental: "true"
state.backend.type: "rocksdb"
```

## 🎯 预期优化效果

### 资源使用优化
- **内存使用**: 降低 40-60%
- **CPU 使用**: 降低 30-50%  
- **启动时间**: 减少 50-70%

### 性能提升
- **算子数量**: 减少 60-80%
- **状态大小**: 降低 40-60%
- **吞吐量**: 提升 20-40%

### 运维改善
- **故障隔离**: 单层故障不影响其他层
- **独立扩缩容**: 按需调整各层资源
- **滚动升级**: 支持无停机更新

## 🔍 故障排查

### 常见问题
1. **内存不足**: 检查 managed memory 配置
2. **启动缓慢**: 检查依赖表是否就绪
3. **数据延迟**: 检查 mini-batch 配置

### 调试命令
```bash
# 查看作业详情
kubectl describe flinkdeployment olap-flink-paimon-foundation-job -n olap

# 查看 Pod 状态
kubectl get pods -n olap -l app=olap-flink-paimon-foundation-job

# 查看日志
kubectl logs -f <pod-name> -n olap
```

## 📝 业务逻辑保证

### 数据一致性验证
所有优化都保持与原 StarRocks 物化视图完全一致的业务逻辑：
- ✅ mv_application_fact 计算结果一致
- ✅ mv_application_wide 关联逻辑一致  
- ✅ mv_application_to_job 聚合结果一致
- ✅ 所有 KPI 视图计算结果一致

### 测试建议
1. 对比优化前后的数据结果
2. 验证关键 KPI 指标的一致性
3. 检查数据时效性和完整性

## 🔄 后续优化方向

1. **动态资源调整**: 根据数据量自动调整资源
2. **智能分区**: 基于查询模式优化分区策略
3. **缓存优化**: 实现跨层级的智能缓存
4. **监控增强**: 添加更详细的业务指标监控
