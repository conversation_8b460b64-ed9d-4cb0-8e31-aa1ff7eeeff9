# 优化的单作业部署配置 - 资源友好版本
# 适用于资源有限的环境，防止 OOM，优化启动速度
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  namespace: olap
  name: olap-flink-paimon-optimized-job
spec:
  flinkConfiguration:
    classloader.resolve-order: parent-first
    execution.checkpointing.dir: "file:///opt/flink/data/checkpoints"
    execution.checkpointing.savepoint-dir: "file:///opt/flink/data/savepoints"
    execution.checkpointing.timeout: "900s"  # 15分钟，给初始化更多时间
    execution.checkpointing.max-concurrent-checkpoints: "1"
    execution.checkpointing.incremental: "true"
    
    # 资源友好的内存配置
    taskmanager.memory.managed.size: "1536m"  # 1.5GB managed memory
    taskmanager.numberOfTaskSlots: "3"        # 减少 slot 数量
    pekko.ask.timeout: 300s                   # 增加超时时间
    state.backend.type: "rocksdb"
    
    # 防止 OOM 的关键配置
    table.exec.sink.upsert-materialize: "NONE"
    table.optimizer.multiple-input-enabled: "false"
    
    # 资源友好的批处理配置
    table.exec.mini-batch.enabled: "true"
    table.exec.mini-batch.allow-latency: "5s"
    table.exec.mini-batch.size: "1000"
    table.exec.state.ttl: "30m"
    
    # JOIN 优化
    table.optimizer.join-reorder-enabled: "true"
    table.optimizer.join.broadcast-threshold: "5MB"
    
    # 缓冲区优化
    table.exec.sink.buffer-flush.max-rows: "5000"
    table.exec.sink.buffer-flush.interval: "5s"
    
    # RocksDB 优化配置
    state.backend.rocksdb.memory.managed: "true"
    state.backend.rocksdb.memory.fixed-per-slot: "256MB"
    
    # 网络缓冲区优化
    taskmanager.memory.network.fraction: "0.1"
    taskmanager.memory.network.min: "64mb"
    taskmanager.memory.network.max: "256mb"

    kubernetes.operator.savepoint.history.max.age: 7d
    kubernetes.operator.savepoint.history.max.count: "3"
    kubernetes.operator.periodic.savepoint.interval: 12h  # 延长保存点间隔
    kubernetes.operator.periodic.checkpoint.interval: 120s  # 延长检查点间隔
    kubernetes.operator.savepoint.cleanup.enabled: "true"
    kubernetes.operator.savepoint.dispose-on-delete: "true"
    kubernetes.operator.snapshot.resource.timeout: 15m  # 增加快照超时
    kubernetes.operator.observer.rest-ready.delay: 30s  # 增加就绪延迟
    kubernetes.operator.reconcile.interval: 60s
    kubernetes.operator.job.restart.failed: "true"

  flinkVersion: v1_20
  image: "minghealtomni/flink_mysql_paimon:**************"
  imagePullPolicy: IfNotPresent
  restartNonce: 1
  serviceAccount: flink
  job:
    args:
      - /opt/flink/cdc-conf/cdc-config.yaml
    entryClass: com.ipg.olap.stream.job.OptimizedPaimonDwdJob
    jarURI: 'local:///opt/flink/app.jar'
    parallelism: 3  # 保守的并行度
    state: running
    upgradeMode: savepoint
  jobManager:
    replicas: 1
    resource:
      cpu: 0.5      # 减少 CPU 需求
      memory: 1536m # 适中的内存配置
  taskManager:
    resource:
      cpu: 2        # 适中的 CPU 配置
      memory: 6144m # 6GB 内存，平衡性能和资源使用
  podTemplate:
    apiVersion: v1
    kind: Pod
    spec:
      affinity:
        podAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchLabels:
                component: jobmanager
                app: olap-flink-paimon-optimized-job
            topologyKey: kubernetes.io/hostname
      securityContext:
        fsGroup: 9999
        runAsUser: 9999
      imagePullSecrets:
      - name: altomni-docker
      containers:
        - name: flink-main-container
          # JVM 优化配置
          env:
            - name: FLINK_ENV_JAVA_OPTS
              value: >-
                -XX:+UseG1GC
                -XX:MaxGCPauseMillis=200
                -XX:+UnlockExperimentalVMOptions
                -XX:+UseCGroupMemoryLimitForHeap
                -XX:MaxRAMFraction=2
                -XX:+HeapDumpOnOutOfMemoryError
                -XX:HeapDumpPath=/opt/flink/data/heapdump.hprof
          envFrom:
            - configMapRef:
                name: olap-flink-paimon-job-configmap
          volumeMounts:
            - mountPath: /opt/flink/cdc-conf
              name: olap-flink-paimon-job-config
            - mountPath: /opt/flink/data
              name: flink-state-volume
          # 资源限制，防止超用
          resources:
            limits:
              memory: "6Gi"
              cpu: "2"
            requests:
              memory: "4Gi"
              cpu: "1"
      volumes:
        - name: olap-flink-paimon-job-config
          configMap:
            name: olap-flink-paimon-job-configmap
        - name: flink-state-volume
          persistentVolumeClaim:
            claimName: olap-flink-paimon-optimized-job-state-pvc
