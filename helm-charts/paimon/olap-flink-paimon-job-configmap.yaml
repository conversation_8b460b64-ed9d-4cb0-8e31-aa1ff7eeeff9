apiVersion: v1
data:
  cdc-config.yaml : |-
    paimon:
        type: "paimon"
        warehouse: "s3://olap-paimon-storage-staging/warehouse"
        metastore: "filesystem"
        s3.endpoint: "https://s3-ap-southeast-1.amazonaws.com"
        s3.access-key: "********************"
        s3.secret-key: "/8vRVAa/Sh8/LGlMlF7OuAx7A5V9dPyeSXvlkoaq"
    action:
        parallelism: "4"
        checkpoint.interval: "60000"
kind: ConfigMap
metadata:
  name: olap-flink-paimon-job-configmap