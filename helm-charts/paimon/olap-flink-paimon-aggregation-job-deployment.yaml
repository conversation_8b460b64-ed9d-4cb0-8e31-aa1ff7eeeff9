# 聚合层作业部署配置 - 处理聚合表
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  namespace: olap
  name: olap-flink-paimon-aggregation-job
spec:
  flinkConfiguration:
    classloader.resolve-order: parent-first
    execution.checkpointing.dir: "file:///opt/flink/data/checkpoints"
    execution.checkpointing.savepoint-dir: "file:///opt/flink/data/savepoints"
    execution.checkpointing.timeout: 1800s
    execution.checkpointing.max-concurrent-checkpoints: "1"
    execution.checkpointing.incremental: "true"
    
    # 聚合层优化配置
    taskmanager.memory.managed.size: "2gb"
    taskmanager.numberOfTaskSlots: "8"
    pekko.ask.timeout: 180s
    state.backend.type: "rocksdb"
    table.exec.sink.upsert-materialize: "NONE"
    
    # 聚合层特定优化 - 注重聚合性能和吞吐量
    table.exec.mini-batch.enabled: "true"
    table.exec.mini-batch.allow-latency: "5s"
    table.exec.mini-batch.size: "5000"
    table.exec.state.ttl: "4h"

    kubernetes.operator.savepoint.history.max.age: 7d
    kubernetes.operator.savepoint.history.max.count: "3"
    kubernetes.operator.periodic.savepoint.interval: 6h
    kubernetes.operator.periodic.checkpoint.interval: 60s
    kubernetes.operator.savepoint.cleanup.enabled: "true"
    kubernetes.operator.savepoint.dispose-on-delete: "true"
    kubernetes.operator.snapshot.resource.timeout: 10m
    kubernetes.operator.observer.rest-ready.delay: 10s
    kubernetes.operator.reconcile.interval: 30s
    kubernetes.operator.job.restart.failed: "true"

  flinkVersion: v1_20
  image: "minghealtomni/flink_mysql_paimon:**************"
  imagePullPolicy: IfNotPresent
  restartNonce: 1
  serviceAccount: flink
  job:
    args:
      - /opt/flink/cdc-conf/cdc-config.yaml
      - aggregation
    entryClass: com.ipg.olap.stream.job.PaimonDwdLayeredJob
    jarURI: 'local:///opt/flink/app.jar'
    parallelism: 8
    state: running
    upgradeMode: savepoint
  jobManager:
    replicas: 1
    resource:
      cpu: 0.4
      memory: 1280m
  taskManager:
    resource:
      cpu: 2.5
      memory: 8192m
  podTemplate:
    apiVersion: v1
    kind: Pod
    spec:
      affinity:
        podAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchLabels:
                component: jobmanager
                app: olap-flink-paimon-aggregation-job
            topologyKey: kubernetes.io/hostname
      securityContext:
        fsGroup: 9999
        runAsUser: 9999
      imagePullSecrets:
      - name: altomni-docker
      containers:
        - name: flink-main-container
          envFrom:
            - configMapRef:
                name: olap-flink-paimon-job-configmap
          volumeMounts:
            - mountPath: /opt/flink/cdc-conf
              name: olap-flink-paimon-job-config
            - mountPath: /opt/flink/data
              name: flink-state-volume
      volumes:
        - name: olap-flink-paimon-job-config
          configMap:
            name: olap-flink-paimon-job-configmap
        - name: flink-state-volume
          persistentVolumeClaim:
            claimName: olap-flink-paimon-aggregation-job-state-pvc
