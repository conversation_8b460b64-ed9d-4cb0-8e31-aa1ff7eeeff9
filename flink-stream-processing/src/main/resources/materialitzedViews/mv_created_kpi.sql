-- 创建公司、升级为正式客户、创建职位、创建候选人的团队层级物化视图（包含子团队数据）
CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_created_kpi
       DISTRIBUTED BY HASH (`team_id`)
REFRESH
       ASYNC EVERY (INTERVAL 10 MINUTE)
ORDER BY (add_date, event_date, team_id)
AS
SELECT base_data.tenant_id,
       hierarchy.parent_team_id                      AS team_id,
       hierarchy.parent_team_name                    AS team_name,
       hierarchy.parent_team_parent_id               AS team_parent_id,
       hierarchy.parent_team_level                   AS team_level,
       hierarchy.parent_team_is_leaf                 AS team_is_leaf,
       base_data.user_id,
       base_data.user_name,
       base_data.user_activated,
       base_data.add_date,
       base_data.event_date,
       entity_id,
       BITMAP_UNION(base_data.created_company_count) AS created_company_count,
       BITMAP_UNION(base_data.upgrade_company_count) AS upgrade_company_count,
       openings,
       BITMAP_UNION(base_data.created_talent_count)  AS created_talent_count
FROM (
         -- 创建公司
         (SELECT u.tenant_id                            AS tenant_id,
                 put.team_id                            AS original_team_id,
                 u.id                                   AS user_id,
                 CONCAT(u.first_name, ' ', u.last_name) AS user_name,
                 u.activated                            AS user_activated,
                 ac.created_date                        AS add_date,
                 ac.created_date                        AS event_date,
                 ac.id                                  AS entity_id,
                 BITMAP_UNION(TO_BITMAP(ac.id))         AS created_company_count,
                 BITMAP_EMPTY()                         AS upgrade_company_count,
                 NULL                                   AS openings,
                 BITMAP_EMPTY()                         AS created_talent_count
          FROM ods_crm.account_company AS ac
                   INNER JOIN ods_crm.business_flow_administrator AS bfa ON ac.id = bfa.account_company_id
                   INNER JOIN ods_apn.user AS u ON bfa.user_id = u.id and u.tenant_id = ac.tenant_id
                   INNER JOIN ods_apn.permission_user_team AS put ON u.id = put.user_id AND put.is_primary = 1
          GROUP BY u.tenant_id, put.team_id, u.id, u.first_name, u.last_name, u.activated, ac.created_date, ac.id,
                   openings)
         UNION ALL
         -- 升级正式客户
         (SELECT u.tenant_id                            AS tenant_id,
                 put.team_id                            AS original_team_id,
                 u.id                                   AS user_id,
                 CONCAT(u.first_name, ' ', u.last_name) AS user_name,
                 u.activated                            AS user_activated,
                 c.request_date                         AS add_date,
                 c.request_date                         AS event_date,
                 c.id                                   AS entity_id,
                 BITMAP_EMPTY()                         AS created_company_count,
                 BITMAP_UNION(TO_BITMAP(c.id))          AS upgrade_company_count,
                 NULL                                   AS openings,
                 BITMAP_EMPTY()                         AS created_talent_count
          FROM ods_apn.company AS c
                   INNER JOIN ods_apn.business_flow_administrator AS bfa ON c.id = bfa.company_id
                   INNER JOIN ods_apn.user AS u ON bfa.user_id = u.id and u.tenant_id = c.tenant_id
                   INNER JOIN ods_apn.permission_user_team AS put ON u.id = put.user_id AND put.is_primary = 1
          WHERE c.active = 30
          GROUP BY u.tenant_id, put.team_id, u.id, u.first_name, u.last_name, u.activated, c.request_date, c.id,
                   openings)
         -- 创建 job
         UNION ALL
         (SELECT u.tenant_id                            AS tenant_id,
                 put.team_id                            AS original_team_id,
                 u.id                                   AS user_id,
                 CONCAT(u.first_name, ' ', u.last_name) AS user_name,
                 u.activated                            AS user_activated,
                 j.created_date                         AS add_date,
                 j.start_date                           AS event_date,
                 j.id                                   AS entity_id,
                 BITMAP_EMPTY()                         AS created_company_count,
                 BITMAP_EMPTY()                         AS upgrade_company_count,
                 j.openings                             AS openings,
                 BITMAP_EMPTY()                         AS created_talent_count

          FROM job AS j
                   INNER JOIN job_user_relation AS ul ON ul.job_id = j.id
                   INNER JOIN permission_user_team put ON put.user_id = ul.user_id and put.is_primary = 1
                   INNER join permission_team pt on pt.id = put.team_id
                   INNER join user u on u.id = ul.user_id and u.tenant_id = j.tenant_id
          GROUP BY u.tenant_id, put.team_id, u.id, u.first_name, u.last_name, u.activated, j.created_date, j.start_date,
                   j.id,
                   j.openings)
         -- 创建候选人
         UNION ALL
         (SELECT u.tenant_id,
                 put.team_id                            AS original_team_id,
                 u.id                                   AS user_id,
                 CONCAT(u.first_name, ' ', u.last_name) AS user_name,
                 u.activated                            AS user_activated,
                 t.created_date                         AS add_date,
                 t.created_date                         AS event_date,
                 t.id                                   AS entity_id,
                 BITMAP_EMPTY()                         AS created_company_count,
                 BITMAP_EMPTY()                         AS upgrade_company_count,
                 NULL                                   AS openings,
                 BITMAP_AGG(t.id)                       AS created_talent_count
          FROM talent t
                   INNER JOIN ods_apn.talent_user_relation tur on t.id = tur.talent_id
                   INNER JOIN ods_apn.user u on u.id = tur.user_id and u.tenant_id = t.tenant_id
                   INNER JOIN ods_apn.permission_user_team put on put.user_id = u.id and put.is_primary = 1
                   INNER JOIN ods_apn.permission_team pt on pt.id = put.team_id
          GROUP BY u.tenant_id, put.team_id, u.id, CONCAT(u.first_name, ' ', u.last_name), u.activated, t.created_date,
                   t.id,
                   t.created_date, openings)) AS base_data
         INNER JOIN ods_apn.mv_team_hierarchy AS hierarchy ON base_data.original_team_id = hierarchy.child_team_id
GROUP BY base_data.tenant_id,
         hierarchy.parent_team_id,
         hierarchy.parent_team_name,
         hierarchy.parent_team_parent_id,
         hierarchy.parent_team_level,
         hierarchy.parent_team_is_leaf,
         base_data.user_id,
         base_data.user_name,
         base_data.user_activated,
         base_data.add_date,
         base_data.event_date,
         base_data.entity_id,
         base_data.openings;