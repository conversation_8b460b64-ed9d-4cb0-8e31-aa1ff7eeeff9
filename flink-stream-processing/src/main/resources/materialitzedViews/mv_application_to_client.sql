CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_application_to_client
       DISTRIBUTED BY HASH (`team_id`)
REFRESH
       ASYNC EVERY (INTERVAL 10 MINUTE)
ORDER BY (add_date, event_date)
AS
SELECT application.tenant_id,
       application.company_id,
       job_id,
       job_pteam_id,
       team_id,
       team_name,
       user_id,
       user_name,
       user_activated,
       add_date                                                                 AS add_date,
       event_date                                                               AS event_date,
       BITMAP_AGG(user_role)                                                    AS user_roles,
       BITMAP_AGG(application.node_id)                                          AS submit_to_client_countNum,
       BITMAP_AGG(IF((ai_score IS NOT NULL), application.talent_recruitment_process_id,
                     NULL))                                                     AS submit_to_client_aiRecommendCountNum,
       BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL), application.talent_recruitment_process_id,
                     NULL))                                                     AS submit_to_client_precisionAiRecommendNum,

       BITMAP_AGG(
               CASE
                   WHEN j.status != 0 THEN NULL
                   WHEN add_date < DATE_SUB(NOW(), INTERVAL 1 YEAR) THEN NULL
                   WHEN pn.stayed_add_date IS NOT NULL THEN
                       IF(hours_diff(pn.stayed_add_date, application.add_date) > 72,
                          application.talent_recruitment_process_id, NULL)
                   WHEN application.node_status = 1 THEN
                       IF(hours_diff(NOW(), GREATEST(application.add_date, application.last_modified_date, application.note_last_modify_date)) > 72,
                          application.talent_recruitment_process_id, NULL)
                   END
       )                                                                        AS submit_to_client_stayedOver,
       BITMAP_AGG(
               CASE
                   WHEN j.status != 0 THEN NULL
                   WHEN add_date < DATE_SUB(NOW(), INTERVAL 1 YEAR) THEN NULL
                   WHEN pn.stayed_add_date IS NOT NULL THEN
                       IF(hours_diff(pn.stayed_add_date, application.add_date) > 1440,
                          application.talent_recruitment_process_id, NULL)
                   WHEN application.node_status = 1 THEN
                       IF(hours_diff(NOW(), GREATEST(application.add_date, application.last_modified_date, application.note_last_modify_date)) > 1440,
                          application.talent_recruitment_process_id, NULL)
                   END
       )                                                                        AS submit_to_client_stayedOver_2Month,

       BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS submit_to_client_current_countNum,
       BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1),
                     application.talent_recruitment_process_id,
                     NULL))                                                     AS submit_to_client_currentAiRecommendNum,
       BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1),
                     application.talent_recruitment_process_id,
                     NULL))                                                     AS submit_to_client_currentPrecisionAiRecommendNum,
       BITMAP_AGG(
               CASE
                   WHEN j.status != 0 THEN NULL
                   WHEN add_date < DATE_SUB(NOW(), INTERVAL 1 YEAR) THEN NULL
                   WHEN application.node_status = 1 THEN
                       IF(hours_diff(NOW(), GREATEST(application.add_date, application.last_modified_date, application.note_last_modify_date)) > 72,
                          application.talent_recruitment_process_id, NULL)
                   END
       )                                                                        AS submit_to_client_currentStayedOver,
       BITMAP_AGG(
               CASE
                   WHEN j.status != 0 THEN NULL
                   WHEN add_date < DATE_SUB(NOW(), INTERVAL 1 YEAR) THEN NULL
                   WHEN application.node_status = 1 THEN
                       IF(hours_diff(NOW(), GREATEST(application.add_date, application.last_modified_date, application.note_last_modify_date)) > 1440,
                          application.talent_recruitment_process_id, NULL)
                   END
       )                                                                        AS submit_to_client_currentStayedOver_2Month
FROM mv_application_wide AS application
         left join (SELECT pn.talent_recruitment_process_id,
                           IF(MIN(next.add_date) IS NOT NULL, MIN(next.add_date),
                              MAX(eli.add_date)) AS stayed_add_date
                    FROM mv_application_fact pn
                             LEFT JOIN mv_application_fact next ON next.talent_recruitment_process_id = pn.talent_recruitment_process_id AND next.node_type > 20
                             LEFT JOIN mv_application_fact eli ON eli.talent_recruitment_process_id = pn.talent_recruitment_process_id AND eli.node_type = -1
                    GROUP BY pn.talent_recruitment_process_id) pn
                   on pn.talent_recruitment_process_id = application.talent_recruitment_process_id
         LEFT JOIN job j ON application.job_id = j.id

WHERE application.node_type = 20
GROUP BY application.tenant_id, application.company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated,
         add_date, event_date;
