CREATE VIEW ods_apn.view_company_application_current_kpi
AS
SELECT base.tenant_id,
       base.company_id,
       base.job_id,
       base.job_pteam_id,
       base.team_id,
       base.team_name,
       base.user_id,
       base.user_name,
       base.user_activated,
       base.add_date,
       base.event_date,
       BITMAP_UNION(base.user_roles)                                        AS user_roles,
       BITMAP_UNION(base.submit_to_job_current_countNum)                    AS submit_to_job_current_countNum,
       BITMAP_UNION(base.submit_to_job_currentAiRecommendNum)               AS submit_to_job_currentAiRecommendNum,
       BITMAP_UNION(base.submit_to_job_currentPrecisionAiRecommendNum)      AS submit_to_job_currentPrecisionAiRecommendNum,
       BITMAP_UNION(base.submit_to_job_currentStayedOver)                   AS submit_to_job_currentStayedOver,
       BITMAP_UNION(base.submit_to_client_current_countNum)                 AS submit_to_client_current_countNum,
       BITMAP_UNION(base.submit_to_client_currentAiRecommendNum)            AS submit_to_client_currentAiRecommendNum,
       BITMAP_UNION(base.submit_to_client_currentPrecisionAiRecommendNum)   AS submit_to_client_currentPrecisionAiRecommendNum,
       BITMAP_UNION(base.submit_to_client_currentStayedOver)                AS submit_to_client_currentStayedOver,
       BITMAP_UNION(base.current_interview1)                                AS current_interview1,
       BITMAP_UNION(base.current_interview2)                                AS current_interview2,
       BITMAP_UNION(base.current_two_or_more_interviews)                    AS current_two_or_more_interviews,
       BITMAP_UNION(base.current_interview_final)                           AS current_interview_final,
       BITMAP_UNION(base.current_interview_total)                           AS current_interview_total,
       BITMAP_UNION(base.unique_interview_talents)                          AS unique_interview_talents,
       BITMAP_UNION(base.current_interview_total_process)                   AS current_interview_total_process,
       BITMAP_UNION(base.currentInterviewTotalAiRecommendNum)               AS currentInterviewTotalAiRecommendNum,
       BITMAP_UNION(base.currentInterviewTotalProcessAiRecommendNum)        AS currentInterviewTotalProcessAiRecommendNum,
       BITMAP_UNION(base.currentInterview1AiRecommendNum)                   AS currentInterview1AiRecommendNum,
       BITMAP_UNION(base.currentInterview2AiRecommendNum)                   AS currentInterview2AiRecommendNum,
       BITMAP_UNION(base.currentTwoOrMoreInterviewsAiRecommendNum)          AS currentTwoOrMoreInterviewsAiRecommendNum,
       BITMAP_UNION(base.currentInterviewFinalAiRecommendNum)               AS currentInterviewFinalAiRecommendNum,
       BITMAP_UNION(base.currentInterviewTotalPrecisionAiRecommendNum)      AS currentInterviewTotalPrecisionAiRecommendNum,
       BITMAP_UNION(base.currentInterviewNumProcessPrecisionAIRecommend)    AS currentInterviewNumProcessPrecisionAIRecommend,
       BITMAP_UNION(base.currentInterview1PrecisionAiRecommendNum)          AS currentInterview1PrecisionAiRecommendNum,
       BITMAP_UNION(base.currentInterview2PrecisionAiRecommendNum)          AS currentInterview2PrecisionAiRecommendNum,
       BITMAP_UNION(base.currentTwoOrMoreInterviewsPrecisionAiRecommendNum) AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
       BITMAP_UNION(base.currentInterviewFinalPrecisionAiRecommendNum)      AS currentInterviewFinalPrecisionAiRecommendNum,
       BITMAP_UNION(base.reserve_current_interview_total)                   AS reserve_current_interview_total,
       BITMAP_UNION(base.reserve_interview_currentAiRecommendNum)           AS reserve_interview_currentAiRecommendNum,
       BITMAP_UNION(base.reserve_interview_currentPrecisionAiRecommendNum)  AS reserve_interview_currentPrecisionAiRecommendNum,
       BITMAP_UNION(base.offer_current_countNum)                            AS offer_current_countNum,
       BITMAP_UNION(base.offer_currentAiRecommendNum)                       AS offer_currentAiRecommendNum,
       BITMAP_UNION(base.offer_currentPrecisionAiRecommendNum)              AS offer_currentPrecisionAiRecommendNum,
       BITMAP_UNION(base.offer_accept_current_countNum)                     AS offer_accept_current_countNum,
       BITMAP_UNION(base.offer_accept_currentAiRecommendNum)                AS offer_accept_currentAiRecommendNum,
       BITMAP_UNION(base.offer_accept_currentPrecisionAiRecommendNum)       AS offer_accept_currentPrecisionAiRecommendNum,
       BITMAP_UNION(base.onboard_current_countNum)                          AS onboard_current_countNum,
       BITMAP_UNION(base.onboard_currentAiRecommendNum)                     AS onboard_currentAiRecommendNum,
       BITMAP_UNION(base.onboard_currentPrecisionAiRecommendNum)            AS onboard_currentPrecisionAiRecommendNum,
       BITMAP_UNION(base.eliminate_current_countNum)                        AS eliminate_current_countNum,
       BITMAP_UNION(base.eliminate_currentAiRecommendNum)                   AS eliminate_currentAiRecommendNum,
       BITMAP_UNION(base.eliminate_currentPrecisionAiRecommendNum)          AS eliminate_currentPrecisionAiRecommendNum

FROM (((SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               submit_to_job_current_countNum,
               submit_to_job_currentAiRecommendNum,
               submit_to_job_currentPrecisionAiRecommendNum,
               submit_to_job_currentStayedOver,
               BITMAP_EMPTY() AS submit_to_client_current_countNum,
               BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentStayedOver,
               BITMAP_EMPTY() AS current_interview1,
               BITMAP_EMPTY() AS current_interview2,
               BITMAP_EMPTY() AS current_two_or_more_interviews,
               BITMAP_EMPTY() AS current_interview_final,
               BITMAP_EMPTY() AS current_interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS current_interview_total_process,
               BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_current_interview_total,
               BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_current_countNum,
               BITMAP_EMPTY() AS offer_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_current_countNum,
               BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_current_countNum,
               BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_current_countNum,
               BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_to_job)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_current_countNum,
               BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
               submit_to_client_current_countNum,
               submit_to_client_currentAiRecommendNum,
               submit_to_client_currentPrecisionAiRecommendNum,
               submit_to_client_currentStayedOver,
               BITMAP_EMPTY() AS current_interview1,
               BITMAP_EMPTY() AS current_interview2,
               BITMAP_EMPTY() AS current_two_or_more_interviews,
               BITMAP_EMPTY() AS current_interview_final,
               BITMAP_EMPTY() AS current_interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS current_interview_total_process,
               BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_current_interview_total,
               BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_current_countNum,
               BITMAP_EMPTY() AS offer_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_current_countNum,
               BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_current_countNum,
               BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_current_countNum,
               BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_to_client)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_current_countNum,
               BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
               BITMAP_EMPTY() AS submit_to_client_current_countNum,
               BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentStayedOver,
               current_interview1,
               current_interview2,
               current_two_or_more_interviews,
               current_interview_final,
               current_interview_total,
               unique_interview_talents,
               current_interview_total_process,
               currentInterviewTotalAiRecommendNum,
               currentInterviewTotalProcessAiRecommendNum,
               currentInterview1AiRecommendNum,
               currentInterview2AiRecommendNum,
               currentTwoOrMoreInterviewsAiRecommendNum,
               currentInterviewFinalAiRecommendNum,
               currentInterviewTotalPrecisionAiRecommendNum,
               currentInterviewNumProcessPrecisionAIRecommend,
               currentInterview1PrecisionAiRecommendNum,
               currentInterview2PrecisionAiRecommendNum,
               currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_current_interview_total,
               BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_current_countNum,
               BITMAP_EMPTY() AS offer_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_current_countNum,
               BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_current_countNum,
               BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_current_countNum,
               BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_interview)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_current_countNum,
               BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
               BITMAP_EMPTY() AS submit_to_client_current_countNum,
               BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentStayedOver,
               BITMAP_EMPTY() AS current_interview1,
               BITMAP_EMPTY() AS current_interview2,
               BITMAP_EMPTY() AS current_two_or_more_interviews,
               BITMAP_EMPTY() AS current_interview_final,
               BITMAP_EMPTY() AS current_interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS current_interview_total_process,
               BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
               reserve_current_interview_total,
               reserve_interview_currentAiRecommendNum,
               reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_current_countNum,
               BITMAP_EMPTY() AS offer_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_current_countNum,
               BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_current_countNum,
               BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_current_countNum,
               BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_reserve_interview)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_current_countNum,
               BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
               BITMAP_EMPTY() AS submit_to_client_current_countNum,
               BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentStayedOver,
               BITMAP_EMPTY() AS current_interview1,
               BITMAP_EMPTY() AS current_interview2,
               BITMAP_EMPTY() AS current_two_or_more_interviews,
               BITMAP_EMPTY() AS current_interview_final,
               BITMAP_EMPTY() AS current_interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS current_interview_total_process,
               BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_current_interview_total,
               BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
               offer_current_countNum,
               offer_currentAiRecommendNum,
               offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_current_countNum,
               BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_current_countNum,
               BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_current_countNum,
               BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_offer)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_current_countNum,
               BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
               BITMAP_EMPTY() AS submit_to_client_current_countNum,
               BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentStayedOver,
               BITMAP_EMPTY() AS current_interview1,
               BITMAP_EMPTY() AS current_interview2,
               BITMAP_EMPTY() AS current_two_or_more_interviews,
               BITMAP_EMPTY() AS current_interview_final,
               BITMAP_EMPTY() AS current_interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS current_interview_total_process,
               BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_current_interview_total,
               BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_current_countNum,
               BITMAP_EMPTY() AS offer_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
               offer_accept_current_countNum,
               offer_accept_currentAiRecommendNum,
               offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_current_countNum,
               BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_current_countNum,
               BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_offer_accept)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_current_countNum,
               BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
               BITMAP_EMPTY() AS submit_to_client_current_countNum,
               BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentStayedOver,
               BITMAP_EMPTY() AS current_interview1,
               BITMAP_EMPTY() AS current_interview2,
               BITMAP_EMPTY() AS current_two_or_more_interviews,
               BITMAP_EMPTY() AS current_interview_final,
               BITMAP_EMPTY() AS current_interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS current_interview_total_process,
               BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_current_interview_total,
               BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_current_countNum,
               BITMAP_EMPTY() AS offer_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_current_countNum,
               BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
               onboard_current_countNum,
               onboard_currentAiRecommendNum,
               onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_current_countNum,
               BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_onboard)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_current_countNum,
               BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
               BITMAP_EMPTY() AS submit_to_client_current_countNum,
               BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_currentStayedOver,
               BITMAP_EMPTY() AS current_interview1,
               BITMAP_EMPTY() AS current_interview2,
               BITMAP_EMPTY() AS current_two_or_more_interviews,
               BITMAP_EMPTY() AS current_interview_final,
               BITMAP_EMPTY() AS current_interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS current_interview_total_process,
               BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_current_interview_total,
               BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_current_countNum,
               BITMAP_EMPTY() AS offer_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_current_countNum,
               BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_current_countNum,
               BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
               eliminate_current_countNum,

               eliminate_currentAiRecommendNum,

               eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_emiminate))) AS base
GROUP BY base.tenant_id,
         base.company_id,
         base.job_id,
         base.job_pteam_id,
         base.team_id,
         base.team_name,
         base.user_id,
         base.user_name,
         base.user_activated,
         base.add_date,
         base.event_date;