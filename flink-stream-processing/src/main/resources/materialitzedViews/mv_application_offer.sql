CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_application_offer
       DISTRIBUTED BY HASH (`team_id`)
REFRESH
       ASYNC EVERY (INTERVAL 10 MINUTE)
ORDER BY (add_date, event_date)
AS
SELECT application.tenant_id,
       application.company_id,
       application.job_id,
       application.job_pteam_id,
       application.team_id,
       application.team_name,
       application.user_id,
       application.user_name,
       application.user_activated,
       application.add_date                                                     AS add_date,
       application.event_date                                                   AS event_date,
       BITMAP_AGG(application.user_role)                                        AS user_roles,
       BITMAP_AGG(application.node_id)                                          AS offer_countNum,
       BITMAP_AGG(IF((application.ai_score IS NOT NULL), application.talent_recruitment_process_id,
                     NULL))                                                     AS offer_aiRecommendCountNum,
       BITMAP_AGG(IF((application.recommend_feedback_id IS NOT NULL), application.talent_recruitment_process_id,
                     NULL))                                                     AS offer_precisionAiRecommendNum,
       BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS offer_current_countNum,
       BITMAP_AGG(IF((application.ai_score IS NOT NULL AND application.node_status = 1),
                     application.talent_recruitment_process_id, NULL))          AS offer_currentAiRecommendNum,
       BITMAP_AGG(IF((application.recommend_feedback_id IS NOT NULL AND application.node_status = 1),
                     application.talent_recruitment_process_id,
                     NULL))                                                     AS offer_currentPrecisionAiRecommendNum,
       BITMAP_AGG(
               CASE
                   WHEN j.status != 0 THEN NULL
                   WHEN to_client.add_date < DATE_SUB(NOW(), INTERVAL 1 YEAR) THEN NULL
                   WHEN to_client.add_date > DATE_SUB(application.add_date, INTERVAL 2 WEEK) THEN
                       application.talent_recruitment_process_id
                   END
       )                                                                        AS offerLess2WeekNum,
       BITMAP_AGG(
               CASE
                   WHEN j.status != 0 THEN NULL
                   WHEN to_client.add_date < DATE_SUB(NOW(), INTERVAL 1 YEAR) THEN NULL
                   WHEN application.node_status = 1 and to_client.add_date > DATE_SUB(application.add_date, INTERVAL 2 WEEK) THEN
                       application.talent_recruitment_process_id
                   END
       )                                                                        AS offer_currentLess2WeekNum

FROM mv_application_wide AS application
         left join mv_application_fact to_client
                   on to_client.talent_recruitment_process_id = application.talent_recruitment_process_id and
                      to_client.node_type = 20
         LEFT JOIN job j on j.id = application.job_id
WHERE application.node_type = 40
GROUP BY application.tenant_id, application.company_id, application.job_id, application.job_pteam_id,
         application.team_id,
         application.team_name, application.user_id, application.user_name, application.user_activated,
         application.add_date, application.event_date;