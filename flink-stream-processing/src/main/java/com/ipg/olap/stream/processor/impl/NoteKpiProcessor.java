package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.optimizer.SqlQueryOptimizer;
import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 笔记类 KPI 处理器
 * 实现 mv_note_kpi 物化视图逻辑，处理人才笔记和跟踪指标
 * 优化笔记、用户、团队层级数据之间的复杂关联查询
 */
public class NoteKpiProcessor extends OptimizedSqlProcessor {

    private static final String TARGET_TABLE = "dwd_apn.dwd_note_kpi";
    private static final String PROCESSOR_NAME = "NoteKpiProcessor";

    public NoteKpiProcessor() {
        super(PROCESSOR_NAME, TARGET_TABLE);
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.KPI;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "ods_apn.talent_note",
            "ods_apn.talent",
            "ods_apn.talent_recruitment_process_note",
            "ods_apn.talent_recruitment_process",
            "ods_apn.talent_recruitment_process_kpi_user",
            "ods_apn.talent_recruitment_process_node",
            "ods_apn.talent_tracking_note",
            "ods_apn.job",
            "ods_apn.user",
            "ods_apn.permission_user_team",
            "dwd_apn.dwd_team_hierarchy"
        };
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableSql = String.format("""
            CREATE TABLE IF NOT EXISTS %s (
                tenant_id BIGINT,
                team_id BIGINT,
                team_name STRING,
                team_parent_id BIGINT,
                team_level INT,
                team_is_leaf BOOLEAN,
                user_id BIGINT,
                user_name STRING,
                user_activated BOOLEAN,
                add_date TIMESTAMP(3),
                call_note_num MULTISET<BIGINT>,
                person_note_num MULTISET<BIGINT>,
                other_note_num MULTISET<BIGINT>,
                email_note_num MULTISET<BIGINT>,
                video_note_num MULTISET<BIGINT>,
                ici_num MULTISET<BIGINT>,
                note_count MULTISET<BIGINT>,
                unique_talent_ids MULTISET<BIGINT>,
                application_note_count_num MULTISET<BIGINT>,
                talent_tracking_note_count_num MULTISET<BIGINT>,
                talent_tracking_note_ids MULTISET<BIGINT>,
                dt STRING,
                PRIMARY KEY (team_id, user_id, add_date, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )
            """, TARGET_TABLE, getStandardPaimonConfig());

        // 直接执行建表 SQL，优化配置已在 WITH 子句中
        tableEnv.executeSql(createTableSql);
        System.out.println("✅ Created target table: " + TARGET_TABLE);
    }

    /**
     * 获取标准的 Paimon 表配置
     */
    private String getStandardPaimonConfig() {
        return String.join(",\n                ",
            "'write-buffer-size' = '256mb'",
            "'sink.parallelism' = '4'", 
            "'lookup.cache.ttl' = '4h'",
            "'compaction.min.file-num' = '2'",
            "'changelog-producer' = 'lookup'",
            "'precommit-compact' = 'true'",
            "'compaction.max.file-num' = '50'",
            "'snapshot.time-retained' = '1h'"
        );
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        // 创建优化的笔记类 KPI 计算 SQL
        String noteKpiSql = buildNoteKpiQuery();
        
        System.out.println("Executing note KPI processing with optimized SQL:");
        System.out.println(noteKpiSql);
        
        statementSet.addInsertSql(noteKpiSql);
    }

    /**
     * 构建笔记类 KPI 计算查询
     * 实现笔记 KPI 逻辑处理 talent_note、application_note、talent_tracking_note
     * 
     * @return 优化的笔记类 KPI SQL 查询
     */
    private String buildNoteKpiQuery() {
        String hints = getOptimizedSqlHints();
        
        // 构建各个笔记类型的子查询
        List<String> subQueries = Arrays.asList(
            buildTalentNoteQuery(),
            buildApplicationNoteQuery(),
            buildTalentTrackingNoteQuery()
        );
        
        // 使用 CTE 构建完整查询
        Map<String, String> cteDefinitions = new HashMap<>();
        cteDefinitions.put("base_data", SqlQueryOptimizer.buildOptimizedUnionAll(subQueries));
        
        String mainQuery = String.format("""
            INSERT INTO %s
            SELECT 
                base_data.tenant_id,
                hierarchy.parent_team_id AS team_id,
                hierarchy.parent_team_name AS team_name,
                hierarchy.parent_team_parent_id AS team_parent_id,
                hierarchy.parent_team_level AS team_level,
                hierarchy.parent_team_is_leaf AS team_is_leaf,
                base_data.user_id,
                base_data.user_name,
                base_data.user_activated,
                base_data.add_date,
                COLLECT(CASE WHEN base_data.call_note_num IS NOT NULL 
                            THEN base_data.call_note_num END) AS call_note_num,
                COLLECT(CASE WHEN base_data.person_note_num IS NOT NULL 
                            THEN base_data.person_note_num END) AS person_note_num,
                COLLECT(CASE WHEN base_data.other_note_num IS NOT NULL 
                            THEN base_data.other_note_num END) AS other_note_num,
                COLLECT(CASE WHEN base_data.email_note_num IS NOT NULL 
                            THEN base_data.email_note_num END) AS email_note_num,
                COLLECT(CASE WHEN base_data.video_note_num IS NOT NULL 
                            THEN base_data.video_note_num END) AS video_note_num,
                COLLECT(CASE WHEN base_data.ici_num IS NOT NULL 
                            THEN base_data.ici_num END) AS ici_num,
                COLLECT(CASE WHEN base_data.note_count IS NOT NULL 
                            THEN base_data.note_count END) AS note_count,
                COLLECT(CASE WHEN base_data.unique_talent_ids IS NOT NULL 
                            THEN base_data.unique_talent_ids END) AS unique_talent_ids,
                COLLECT(CASE WHEN base_data.application_note_count_num IS NOT NULL 
                            THEN base_data.application_note_count_num END) AS application_note_count_num,
                COLLECT(CASE WHEN base_data.talent_tracking_note_count_num IS NOT NULL 
                            THEN base_data.talent_tracking_note_count_num END) AS talent_tracking_note_count_num,
                COLLECT(CASE WHEN base_data.talent_tracking_note_ids IS NOT NULL 
                            THEN base_data.talent_tracking_note_ids END) AS talent_tracking_note_ids,
                DATE_FORMAT(LOCALTIMESTAMP, 'yyyy-MM-dd') as dt
            FROM base_data
            INNER JOIN dwd_apn.dwd_team_hierarchy FOR SYSTEM_TIME AS OF PROCTIME() AS hierarchy 
                ON base_data.original_team_id = hierarchy.child_team_id
            GROUP BY 
                base_data.tenant_id,
                hierarchy.parent_team_id,
                hierarchy.parent_team_name,
                hierarchy.parent_team_parent_id,
                hierarchy.parent_team_level,
                hierarchy.parent_team_is_leaf,
                base_data.user_id,
                base_data.user_name,
                base_data.user_activated,
                base_data.add_date
            """, TARGET_TABLE);
        
        String completeSql = SqlQueryOptimizer.buildOptimizedMultipleCTE(cteDefinitions, mainQuery);
        
        return hints + "\n" + completeSql;
    }

    /**
     * 构建人才笔记子查询
     * 处理不同类型的人才笔记（电话、面谈、其他、邮件、视频、ICI）
     */
    private String buildTalentNoteQuery() {
        return """
            SELECT 
                u.tenant_id,
                put.team_id AS original_team_id,
                u.id AS user_id,
                CONCAT(u.first_name, ' ', u.last_name) AS user_name,
                u.activated AS user_activated,
                tn.created_date AS add_date,
                CASE WHEN tn.note_type = 0 THEN tn.id END AS call_note_num,
                CASE WHEN tn.note_type = 1 THEN tn.id END AS person_note_num,
                CASE WHEN tn.note_type = 2 THEN tn.id END AS other_note_num,
                CASE WHEN tn.note_type = 3 THEN tn.id END AS email_note_num,
                CASE WHEN tn.note_type = 4 THEN tn.id END AS video_note_num,
                CASE WHEN tn.note_type = 5 THEN tn.id END AS ici_num,
                CASE WHEN tn.agency_id IS NULL THEN tn.id END AS note_count,
                tn.talent_id AS unique_talent_ids,
                CAST(NULL AS BIGINT) AS application_note_count_num,
                CAST(NULL AS BIGINT) AS talent_tracking_note_count_num,
                CAST(NULL AS BIGINT) AS talent_tracking_note_ids
            FROM ods_apn.talent_note AS tn
            INNER JOIN ods_apn.talent AS t 
                ON tn.talent_id = t.id
            INNER JOIN ods_apn.user AS u 
                ON u.id = tn.puser_id AND t.tenant_id = u.tenant_id
            INNER JOIN ods_apn.permission_user_team AS put 
                ON put.user_id = tn.puser_id AND put.is_primary = 1
            """;
    }

    /**
     * 构建申请笔记子查询
     * 处理人才招聘流程中的笔记
     */
    private String buildApplicationNoteQuery() {
        return """
            SELECT 
                u.tenant_id,
                put.team_id AS original_team_id,
                u.id AS user_id,
                CONCAT(u.first_name, ' ', u.last_name) AS user_name,
                u.activated AS user_activated,
                node.created_date AS add_date,
                CAST(NULL AS BIGINT) AS call_note_num,
                CAST(NULL AS BIGINT) AS person_note_num,
                CAST(NULL AS BIGINT) AS other_note_num,
                CAST(NULL AS BIGINT) AS email_note_num,
                CAST(NULL AS BIGINT) AS video_note_num,
                CAST(NULL AS BIGINT) AS ici_num,
                CAST(NULL AS BIGINT) AS note_count,
                CAST(NULL AS BIGINT) AS unique_talent_ids,
                node.node_id AS application_note_count_num,
                CAST(NULL AS BIGINT) AS talent_tracking_note_count_num,
                CAST(NULL AS BIGINT) AS talent_tracking_note_ids
            FROM ods_apn.talent_recruitment_process_note AS node
            INNER JOIN ods_apn.talent_recruitment_process AS trp 
                ON trp.id = node.talent_recruitment_process_id
            INNER JOIN ods_apn.talent_recruitment_process_kpi_user AS ul 
                ON ul.talent_recruitment_process_id = trp.id
            INNER JOIN ods_apn.job AS j 
                ON j.id = trp.job_id
            INNER JOIN ods_apn.talent AS t 
                ON t.id = trp.talent_id
            INNER JOIN ods_apn.talent_recruitment_process_node AS trpn 
                ON trp.id = trpn.talent_recruitment_process_id
            INNER JOIN ods_apn.permission_user_team AS put 
                ON put.user_id = node.user_id AND put.is_primary = 1
            INNER JOIN ods_apn.user AS u 
                ON u.id = put.user_id AND node.tenant_id = u.tenant_id
            """;
    }

    /**
     * 构建人才跟踪笔记子查询
     * 处理人才跟踪过程中的笔记
     */
    private String buildTalentTrackingNoteQuery() {
        return """
            SELECT 
                u.tenant_id,
                put.team_id AS original_team_id,
                u.id AS user_id,
                CONCAT(u.first_name, ' ', u.last_name) AS user_name,
                u.activated AS user_activated,
                ttn.created_date AS add_date,
                CAST(NULL AS BIGINT) AS call_note_num,
                CAST(NULL AS BIGINT) AS person_note_num,
                CAST(NULL AS BIGINT) AS other_note_num,
                CAST(NULL AS BIGINT) AS email_note_num,
                CAST(NULL AS BIGINT) AS video_note_num,
                CAST(NULL AS BIGINT) AS ici_num,
                CAST(NULL AS BIGINT) AS note_count,
                CAST(NULL AS BIGINT) AS unique_talent_ids,
                CAST(NULL AS BIGINT) AS application_note_count_num,
                ttn.id AS talent_tracking_note_count_num,
                ttn.id AS talent_tracking_note_ids
            FROM ods_apn.talent_tracking_note AS ttn
            INNER JOIN ods_apn.talent AS t 
                ON t.id = ttn.synced_talent_id
            INNER JOIN ods_apn.user AS u 
                ON u.id = ttn.user_id AND ttn.tenant_id = u.tenant_id
            INNER JOIN ods_apn.permission_user_team AS put 
                ON put.user_id = ttn.user_id AND put.is_primary = 1
            """;
    }

    /**
     * 获取处理器性能信息
     * 
     * @return 性能配置信息
     */
    @Override
    public String getPerformanceInfo() {
        return super.getPerformanceInfo() + 
               ", Optimization: BITMAP_AGG equivalent with COLLECT for note types, Temporal join with team hierarchy";
    }
}