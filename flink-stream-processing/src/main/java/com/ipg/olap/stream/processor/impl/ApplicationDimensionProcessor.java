package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import com.ipg.olap.stream.optimizer.SqlQueryOptimizer;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

/**
 * 优化的应用维度处理器
 * 使用 Flink SQL 最佳实践，包括时态关联查询和 lookup 缓存优化
 */
public class ApplicationDimensionProcessor extends OptimizedSqlProcessor {

    public ApplicationDimensionProcessor() {
        super("ApplicationDimensionProcessor", "dwd_apn.dwd_application_dimension");
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.DIMENSION;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "talent_recruitment_process",
            "recruitment_process",
            "job",
            "company",
            "job_talent_recommend_feedback",
            "credit_transaction"
        };
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableDDL = """
            CREATE TABLE IF NOT EXISTS %s (
                tenant_id BIGINT,
                company_id BIGINT,
                company_name STRING,
                job_id BIGINT,
                job_title STRING,
                job_pteam_id BIGINT,
                talent_recruitment_process_id BIGINT,
                talent_id BIGINT,
                ai_score DOUBLE,
                recommend_feedback_id BIGINT,
                dt STRING,
                PRIMARY KEY (talent_recruitment_process_id, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )""".formatted(targetTableName, getOptimizedPaimonTableConfig());

        // 直接执行建表 SQL，优化配置已在 WITH 子句中
        tableEnv.executeSql(createTableDDL);
        
        System.out.println("Optimized DWD table created: " + getTargetTableName());
        System.out.println("Performance info: " + getPerformanceInfo());
    }

    /**
     * 获取优化的 Paimon 表配置字符串
     * 基于维度表的特性进行优化配置
     */
    private String getOptimizedPaimonTableConfig() {
        return String.join(",\n                ",
            "'write-buffer-size' = '128mb'",
            "'sink.parallelism' = '2'", 
            "'lookup.cache.ttl' = '24h'",
            "'lookup.cache.max-rows' = '10000'",
            "'compaction.min.file-num' = '2'",
            "'changelog-producer' = 'lookup'",
            "'precommit-compact' = 'true'",
            "'compaction.max.file-num' = '50'",
            "'snapshot.time-retained' = '1h'"//,
//            "'scan.snapshot-id' = 'latest'"
        );
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        // 使用 SqlQueryOptimizer 构建优化的时态关联查询
        String optimizedDimensionSQL = buildOptimizedDimensionQuery();
        
        System.out.println("Executing optimized dimension processing SQL:");
        System.out.println(optimizedDimensionSQL);

        statementSet.addInsertSql(optimizedDimensionSQL);
    }

    /**
     * 构建优化的维度查询 SQL
     * 使用时态关联查询和 lookup 缓存优化
     */
    private String buildOptimizedDimensionQuery() {
        // 获取性能提示
        String hints = getOptimizedSqlHints();
        
        // 构建主查询，使用优化的时态关联
        String mainQuery = String.format("""
            INSERT INTO %s
            SELECT
                trp.tenant_id,
                c.id AS company_id,
                c.full_business_name AS company_name,
                j.id AS job_id,
                j.title AS job_title,
                j.pteam_id AS job_pteam_id,
                trp.id AS talent_recruitment_process_id,
                trp.talent_id AS talent_id,
                trp.ai_score AS ai_score,
                rf.id AS recommend_feedback_id,
                DATE_FORMAT(trp.created_date, 'yyyy-MM') AS dt
            FROM (
                SELECT trp.*,
                       PROCTIME() AS proc_time
                FROM ods_apn.talent_recruitment_process trp
            ) AS trp
            LEFT JOIN ods_apn.job FOR SYSTEM_TIME AS OF trp.proc_time AS j
                ON trp.job_id = j.id
            LEFT JOIN ods_apn.company FOR SYSTEM_TIME AS OF trp.proc_time AS c
                ON j.company_id = c.id
            LEFT JOIN (
                SELECT DISTINCT
                    CASE
                        WHEN (rf.reason = 'UNLOCK_CANDIDATE' OR rf.reason = 'ADD_TO_TALENT')
                            THEN CAST(ct.talent_id AS BIGINT)
                        ELSE CAST(rf.talent_id AS BIGINT)
                    END AS talent_id,
                    rf.id,
                    rf.job_id
                FROM ods_apn.job_talent_recommend_feedback AS rf
                LEFT JOIN ods_apn.credit_transaction AS ct
                    ON ct.profile_id = rf.talent_id
                WHERE rf.reason IN ('ADD_TO_POSITION', 'ADD_TO_ASSOCIATION_JOB_FOLDER', 'UNLOCK_CANDIDATE', 'ADD_TO_TALENT')
            ) rf ON (rf.job_id = trp.job_id) AND (rf.talent_id = trp.talent_id)
            """, targetTableName);

        // 添加性能提示
        return hints + "\n" + mainQuery;
    }

    /**
     * 构建优化的公司查找查询
     * 使用 SqlQueryOptimizer 的时态关联优化
     */
    private String buildOptimizedCompanyLookup() {
        return SqlQueryOptimizer.buildOptimizedTemporalJoin(
            "ods_apn.job",
            "ods_apn.company",
            "j.company_id = c.id",
            "j.*, c.id as company_id, c.full_business_name as company_name"
        );
    }

    /**
     * 构建优化的职位查找查询
     * 使用 SqlQueryOptimizer 的时态关联优化
     */
    private String buildOptimizedJobLookup() {
        return SqlQueryOptimizer.buildOptimizedTemporalJoin(
            "ods_apn.talent_recruitment_process",
            "ods_apn.job",
            "trp.job_id = j.id",
            "trp.*, j.id as job_id, j.title as job_title, j.pteam_id as job_pteam_id, j.company_id"
        );
    }
}
