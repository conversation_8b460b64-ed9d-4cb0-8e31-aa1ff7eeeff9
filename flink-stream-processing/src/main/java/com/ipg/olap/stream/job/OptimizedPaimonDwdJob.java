package com.ipg.olap.stream.job;

import com.ipg.olap.stream.config.EnvConfiguration;
import com.ipg.olap.stream.config.PaimonConfig;
import com.ipg.olap.stream.factory.TableProcessorFactory;
import com.ipg.olap.stream.monitor.MemoryOptimizer;
import com.ipg.olap.stream.monitor.PerformanceMonitor;
import com.ipg.olap.stream.processor.AbstractTableProcessor;
import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.RestOptions;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 优化的 Paimon DWD 作业 - 资源友好版本
 * 
 * 针对资源有限环境的优化策略：
 * 1. 保持单一作业，减少资源开销
 * 2. 优化内存使用，防止 OOM
 * 3. 分阶段执行处理器，减少并发压力
 * 4. 智能配置调优，适应小数据量场景
 * 
 * 适用场景：
 * - 资源有限的环境（CPU < 8核，内存 < 16GB）
 * - 数据量较小（< 500万条/表）
 * - 可以接受初始化阶段较慢
 */
public class OptimizedPaimonDwdJob {

    private static final Logger log = LoggerFactory.getLogger(OptimizedPaimonDwdJob.class);

    public static void main(String[] args) {
        try {
            PerformanceMonitor.monitorJobStartup("OptimizedPaimonDwdJob", "single");

            // 启动内存监控
            MemoryOptimizer.startMemoryMonitoring(30); // 每30秒检查一次内存

            log.info("🚀 Starting Optimized Paimon DWD Job (Resource-Friendly Version)");
            log.info(MemoryOptimizer.getMemoryReport());

            // 创建优化的执行环境
            StreamExecutionEnvironment env = createResourceFriendlyEnvironment(args);
            StreamTableEnvironment tableEnv = createOptimizedTableEnvironment(env);

            // 应用资源友好的优化配置
            applyResourceFriendlyOptimizations(tableEnv);

            // 显示所有注册的处理器
            TableProcessorFactory.printAllProcessors();

            // 分阶段执行处理器，减少内存压力
            executeProcessorsInPhases(tableEnv);

            PerformanceMonitor.monitorJobCompletion("OptimizedPaimonDwdJob", "single");

            // 停止内存监控
            MemoryOptimizer.stopMemoryMonitoring();

        } catch (Exception e) {
            log.error("❌ Job execution failed", e);
            log.error(MemoryOptimizer.getMemoryReport());
            log.error(MemoryOptimizer.getOptimizationSuggestions());
            System.exit(1);
        }
    }

    /**
     * 创建资源友好的执行环境
     */
    private static StreamExecutionEnvironment createResourceFriendlyEnvironment(String[] args) {
                Configuration flinkConfig = new Configuration();
        // 关闭 upsert-materialize
//        flinkConfig.setString("table.exec.sink.upsert-materialize", "NONE");
//        flinkConfig.setString("state.backend.type", "rocksdb");
//        flinkConfig.setString("execution.checkpointing.dir", "file:///Users/<USER>/ipg/backend/apn-flink-ETL/flink/data/checkpoints");
//
//        flinkConfig.setInteger(RestOptions.PORT, 8082);
//        StreamExecutionEnvironment env = StreamExecutionEnvironment.createLocalEnvironmentWithWebUI(flinkConfig);
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        EnvConfiguration.initConfig(args[0]);
        Map<String, String> actionConfig = EnvConfiguration.getCdcActionConfig();
        
        // 保守的并行度设置，避免资源竞争
        int parallelism = Math.min(4, Integer.parseInt(actionConfig.getOrDefault("parallelism", "3")));
        env.setParallelism(parallelism);
        
        // 较长的检查点间隔，减少 I/O 压力
        int checkpointInterval = Integer.parseInt(actionConfig.getOrDefault("checkpoint.interval", "120000")); // 2分钟
        env.enableCheckpointing(checkpointInterval);
        
        log.info("✅ Resource-friendly environment created with parallelism: {}", parallelism);
        return env;
    }

    /**
     * 创建优化的表环境
     */
    private static StreamTableEnvironment createOptimizedTableEnvironment(StreamExecutionEnvironment env) {
        EnvironmentSettings settings = EnvironmentSettings
            .newInstance()
            .inStreamingMode()
            .build();
        
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env, settings);
        
        // 设置 Paimon 环境
        PaimonConfig.setupPaimonEnvironment(tableEnv);
        
        return tableEnv;
    }

    /**
     * 应用资源友好的优化配置
     */
    private static void applyResourceFriendlyOptimizations(StreamTableEnvironment tableEnv) {
        Configuration config = tableEnv.getConfig().getConfiguration();
        
        // 内存优化配置
        config.setString("table.exec.mini-batch.enabled", "true");
        config.setString("table.exec.mini-batch.allow-latency", "5s");  // 较长延迟，减少频繁处理
        config.setString("table.exec.mini-batch.size", "1000");         // 较小批次，减少内存压力
        
        // 状态管理优化
        config.setString("table.exec.state.ttl", "30m");               // 较短 TTL，及时清理状态
        
        // JOIN 优化
        config.setString("table.optimizer.join-reorder-enabled", "true");
        config.setString("table.optimizer.join.broadcast-threshold", "5MB");  // 较小广播阈值
        
        // 资源管理优化
        config.setString("table.exec.resource.default-parallelism", "3");
        config.setString("table.exec.sink.buffer-flush.max-rows", "5000");    // 较小缓冲区
        config.setString("table.exec.sink.buffer-flush.interval", "5s");
        
        // 防止 OOM 的关键配置
        config.setString("table.exec.sink.upsert-materialize", "NONE");       // 关闭物化，节省内存
        config.setString("table.optimizer.multiple-input-enabled", "false");  // 简化执行图
        
        log.info("✅ Resource-friendly optimizations applied");
        PerformanceMonitor.monitorTableEnvironmentConfig(tableEnv, "resource-friendly");
    }

    /**
     * 优化的处理器执行策略
     *
     * 策略：
     * 1. 按优先级顺序添加处理器到同一个 StatementSet
     * 2. 基础表优先（Fact + Dimension）
     * 3. 然后是依赖表（Wide + Aggregation）
     * 4. 一次性提交所有处理器，但通过优化配置减少内存压力
     */
    private static void executeProcessorsInPhases(StreamTableEnvironment tableEnv) throws Exception {
        log.info("📋 Executing processors with optimized strategy");

        // 创建单一 StatementSet
        StatementSet statementSet = tableEnv.createStatementSet();

        // 按优先级顺序处理器列表
        List<String> orderedProcessors = Arrays.asList(
            "ApplicationFactProcessor",      // 优先级1：事实表
            "ApplicationDimensionProcessor", // 优先级2：维度表
            "ApplicationWideProcessor",      // 优先级3：宽表
            "ToJobProcessor"                 // 优先级4：聚合表
        );

        log.info("🔄 Adding processors in priority order...");

        // 按顺序添加处理器，每个处理器之间进行内存清理
        for (int i = 0; i < orderedProcessors.size(); i++) {
            String processorName = orderedProcessors.get(i);

            if (TableProcessorFactory.hasProcessor(processorName)) {
                AbstractTableProcessor processor = (AbstractTableProcessor) TableProcessorFactory.getProcessor(processorName);

                PerformanceMonitor.startProcessorMonitoring(processorName);

                log.info("🔄 Adding processor {}/{}: {}", i + 1, orderedProcessors.size(), processorName);
                processor.process(tableEnv, statementSet);

                PerformanceMonitor.endProcessorMonitoring(processorName);

                // 输出性能信息
                if (processor instanceof OptimizedSqlProcessor optimizedProcessor) {
                    log.info("📈 Performance info: {}", optimizedProcessor.getPerformanceInfo());
                }

                // 每添加一个处理器后进行内存清理
                MemoryOptimizer.performMemoryCleanup();

                // 显示当前内存状态
                log.info("💾 Current memory status after adding {}: {}", processorName,
                    MemoryOptimizer.getMemoryReport().split("\n")[3]); // 只显示内存使用行

            } else {
                log.warn("⚠️ Processor not found: {}", processorName);
            }
        }

        // 最终提交所有处理器
        log.info("📤 Submitting all processors...");
        log.info(MemoryOptimizer.getMemoryReport());

        statementSet.execute().await();

        log.info("🎉 All processors completed successfully!");
    }


}
