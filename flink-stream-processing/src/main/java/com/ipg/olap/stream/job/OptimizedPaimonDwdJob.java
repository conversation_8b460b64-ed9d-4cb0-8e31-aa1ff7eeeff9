package com.ipg.olap.stream.job;

import com.ipg.olap.stream.config.EnvConfiguration;
import com.ipg.olap.stream.config.PaimonConfig;
import com.ipg.olap.stream.factory.TableProcessorFactory;
import com.ipg.olap.stream.monitor.MemoryOptimizer;
import com.ipg.olap.stream.monitor.PerformanceMonitor;
import com.ipg.olap.stream.processor.AbstractTableProcessor;
import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.RestOptions;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 优化的 Paimon DWD 作业 - 资源友好版本
 * 
 * 针对资源有限环境的优化策略：
 * 1. 保持单一作业，减少资源开销
 * 2. 优化内存使用，防止 OOM
 * 3. 分阶段执行处理器，减少并发压力
 * 4. 智能配置调优，适应小数据量场景
 * 
 * 适用场景：
 * - 资源有限的环境（CPU < 8核，内存 < 16GB）
 * - 数据量较小（< 500万条/表）
 * - 可以接受初始化阶段较慢
 */
public class OptimizedPaimonDwdJob {

    private static final Logger log = LoggerFactory.getLogger(OptimizedPaimonDwdJob.class);

    public static void main(String[] args) {
        try {
            PerformanceMonitor.monitorJobStartup("OptimizedPaimonDwdJob", "single");

            // 启动内存监控
            MemoryOptimizer.startMemoryMonitoring(30); // 每30秒检查一次内存

            log.info("🚀 Starting Optimized Paimon DWD Job (Resource-Friendly Version)");
            log.info(MemoryOptimizer.getMemoryReport());

            // 创建优化的执行环境
            StreamExecutionEnvironment env = createResourceFriendlyEnvironment(args);
            StreamTableEnvironment tableEnv = createOptimizedTableEnvironment(env);

            // 应用资源友好的优化配置
            applyResourceFriendlyOptimizations(tableEnv);

            // 显示所有注册的处理器
            TableProcessorFactory.printAllProcessors();

            // 分阶段执行处理器，减少内存压力
            executeProcessorsInPhases(tableEnv);

            PerformanceMonitor.monitorJobCompletion("OptimizedPaimonDwdJob", "single");

            // 停止内存监控
            MemoryOptimizer.stopMemoryMonitoring();

        } catch (Exception e) {
            log.error("❌ Job execution failed", e);
            log.error(MemoryOptimizer.getMemoryReport());
            log.error(MemoryOptimizer.getOptimizationSuggestions());
            System.exit(1);
        }
    }

    /**
     * 创建资源友好的执行环境
     */
    private static StreamExecutionEnvironment createResourceFriendlyEnvironment(String[] args) {
                Configuration flinkConfig = new Configuration();
        // 关闭 upsert-materialize
        flinkConfig.setString("table.exec.sink.upsert-materialize", "NONE");
        flinkConfig.setString("state.backend.type", "rocksdb");
        flinkConfig.setString("execution.checkpointing.dir", "file:///Users/<USER>/ipg/backend/apn-flink-ETL/flink/data/checkpoints");

        flinkConfig.setInteger(RestOptions.PORT, 8082);
        StreamExecutionEnvironment env = StreamExecutionEnvironment.createLocalEnvironmentWithWebUI(flinkConfig);
//        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        EnvConfiguration.initConfig(args[0]);
        Map<String, String> actionConfig = EnvConfiguration.getCdcActionConfig();
        
        // 保守的并行度设置，避免资源竞争
        int parallelism = Math.min(4, Integer.parseInt(actionConfig.getOrDefault("parallelism", "3")));
        env.setParallelism(parallelism);
        
        // 较长的检查点间隔，减少 I/O 压力
        int checkpointInterval = Integer.parseInt(actionConfig.getOrDefault("checkpoint.interval", "120000")); // 2分钟
        env.enableCheckpointing(checkpointInterval);
        
        log.info("✅ Resource-friendly environment created with parallelism: {}", parallelism);
        return env;
    }

    /**
     * 创建优化的表环境
     */
    private static StreamTableEnvironment createOptimizedTableEnvironment(StreamExecutionEnvironment env) {
        EnvironmentSettings settings = EnvironmentSettings
            .newInstance()
            .inStreamingMode()
            .build();
        
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env, settings);
        
        // 设置 Paimon 环境
        PaimonConfig.setupPaimonEnvironment(tableEnv);
        
        return tableEnv;
    }

    /**
     * 应用资源友好的优化配置
     */
    private static void applyResourceFriendlyOptimizations(StreamTableEnvironment tableEnv) {
        Configuration config = tableEnv.getConfig().getConfiguration();
        
        // 内存优化配置
        config.setString("table.exec.mini-batch.enabled", "true");
        config.setString("table.exec.mini-batch.allow-latency", "5s");  // 较长延迟，减少频繁处理
        config.setString("table.exec.mini-batch.size", "1000");         // 较小批次，减少内存压力
        
        // 状态管理优化
        config.setString("table.exec.state.ttl", "30m");               // 较短 TTL，及时清理状态
        
        // JOIN 优化
        config.setString("table.optimizer.join-reorder-enabled", "true");
        config.setString("table.optimizer.join.broadcast-threshold", "5MB");  // 较小广播阈值
        
        // 资源管理优化
        config.setString("table.exec.resource.default-parallelism", "3");
        config.setString("table.exec.sink.buffer-flush.max-rows", "5000");    // 较小缓冲区
        config.setString("table.exec.sink.buffer-flush.interval", "5s");
        
        // 防止 OOM 的关键配置
        config.setString("table.exec.sink.upsert-materialize", "NONE");       // 关闭物化，节省内存
        config.setString("table.optimizer.multiple-input-enabled", "false");  // 简化执行图
        
        log.info("✅ Resource-friendly optimizations applied");
        PerformanceMonitor.monitorTableEnvironmentConfig(tableEnv, "resource-friendly");
    }

    /**
     * 分阶段执行处理器，减少内存压力
     * 
     * 策略：
     * 1. 先执行基础表（Fact + Dimension）
     * 2. 等待一段时间让内存稳定
     * 3. 再执行依赖表（Wide + Aggregation）
     */
    private static void executeProcessorsInPhases(StreamTableEnvironment tableEnv) throws Exception {
        log.info("📋 Executing processors in phases to optimize memory usage");
        
        // 阶段1：基础表处理
        List<String> phase1Processors = Arrays.asList(
            "ApplicationFactProcessor",
            "ApplicationDimensionProcessor"
        );
        
        log.info("🔄 Phase 1: Processing foundation tables");
        StatementSet phase1StatementSet = tableEnv.createStatementSet();
        executeProcessorGroup(tableEnv, phase1StatementSet, phase1Processors);
        
        // 提交第一阶段
        log.info("📤 Submitting Phase 1 processors...");
        phase1StatementSet.execute().await();
        log.info("✅ Phase 1 completed successfully");
        
        // 等待一段时间，让系统稳定
        log.info("⏳ Waiting for system stabilization...");
        Thread.sleep(30000); // 30秒
        
        // 阶段2：依赖表处理
        List<String> phase2Processors = Arrays.asList(
            "ApplicationWideProcessor",
            "ToJobProcessor"
        );
        
        log.info("🔄 Phase 2: Processing dependent tables");
        StatementSet phase2StatementSet = tableEnv.createStatementSet();
        executeProcessorGroup(tableEnv, phase2StatementSet, phase2Processors);
        
        // 提交第二阶段
        log.info("📤 Submitting Phase 2 processors...");
        phase2StatementSet.execute().await();
        log.info("✅ Phase 2 completed successfully");
        
        log.info("🎉 All phases completed successfully!");
    }

    /**
     * 执行一组处理器
     */
    private static void executeProcessorGroup(StreamTableEnvironment tableEnv, 
                                            StatementSet statementSet, 
                                            List<String> processorNames) {
        for (String processorName : processorNames) {
            if (TableProcessorFactory.hasProcessor(processorName)) {
                AbstractTableProcessor processor = (AbstractTableProcessor) TableProcessorFactory.getProcessor(processorName);
                
                PerformanceMonitor.startProcessorMonitoring(processorName);
                
                log.info("🔄 Processing: {}", processorName);
                processor.process(tableEnv, statementSet);
                
                PerformanceMonitor.endProcessorMonitoring(processorName);
                
                // 输出性能信息
                if (processor instanceof OptimizedSqlProcessor optimizedProcessor) {
                    log.info("📈 Performance info: {}", optimizedProcessor.getPerformanceInfo());
                }
                
                // 强制垃圾回收，释放内存
                System.gc();
                
            } else {
                log.warn("⚠️ Processor not found: {}", processorName);
            }
        }
    }
}
