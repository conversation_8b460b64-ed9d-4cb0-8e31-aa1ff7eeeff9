package com.ipg.olap.stream.config;

import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * Paimon 配置管理类
 * 集中管理 Paimon 相关的配置和初始化逻辑
 */
public class PaimonConfig {
    private static final Logger log = LoggerFactory.getLogger(PaimonConfig.class);

    // Paimon Catalog 配置
    public static final String CATALOG_NAME = "paimon_catalog";

    // 数据库名称
    public static final String ODS_DATABASE = "ods_apn";
    public static final String DWD_DATABASE = "dwd_apn";

    /**
     * 创建 Paimon Catalog
     *
     * @param tableEnv Flink Table Environment
     */
    public static void createPaimonCatalog(StreamTableEnvironment tableEnv) {
        Map<String, String> paimonConfig = EnvConfiguration.getPaimonConfig();
        String catalogConfig = paimonConfig.entrySet().stream()
            .map(entry -> "'%s' = '%s'".formatted(entry.getKey(), entry.getValue()))
            .collect(Collectors.joining(",\n "));
        String catalogDDL = """
            CREATE CATALOG IF NOT EXISTS %s WITH (
                %s
            )""".formatted(CATALOG_NAME, catalogConfig);

        tableEnv.executeSql(catalogDDL);
        log.info("Paimon Catalog created: {}", CATALOG_NAME);
    }

    /**
     * 初始化数据库和环境
     *
     * @param tableEnv Flink Table Environment
     */
    public static void initializeDatabases(StreamTableEnvironment tableEnv) {
        // 使用 Paimon Catalog
        tableEnv.executeSql("USE CATALOG " + CATALOG_NAME);

        // 确保数据库存在
        tableEnv.executeSql("CREATE DATABASE IF NOT EXISTS " + ODS_DATABASE);
        tableEnv.executeSql("CREATE DATABASE IF NOT EXISTS " + DWD_DATABASE);
        
        // 不设置默认数据库，要求所有表名都使用完全限定名
        log.info("Databases initialized: {} and {}", ODS_DATABASE, DWD_DATABASE);
        log.info("All table references must use fully qualified names (database.table)");
    }

    /**
     * 获取完整的 Paimon 环境设置
     *
     * @param tableEnv Flink Table Environment
     */
    public static void setupPaimonEnvironment(StreamTableEnvironment tableEnv) {
        createPaimonCatalog(tableEnv);
        initializeDatabases(tableEnv);

    }

}
