package com.ipg.olap.stream.optimizer;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

/**
 * Flink SQL 执行环境优化器
 * 提供 TableEnvironment 的 SQL 执行优化参数配置
 */
public class FlinkSqlOptimizer {

    /**
     * 配置 TableEnvironment 的 SQL 执行优化参数
     * 包括 join 重排序、广播阈值、mini-batch 优化、状态 TTL 等
     * 
     * @param tableEnv Flink StreamTableEnvironment
     */
    public static void optimizeTableEnvironment(StreamTableEnvironment tableEnv) {
        Configuration config = tableEnv.getConfig().getConfiguration();

        // Join 优化 - 提高多表关联性能
        config.setString("table.optimizer.join-reorder-enabled", "true");
        config.setString("table.optimizer.join.broadcast-threshold", "10MB");
        
        // Mini-batch 优化 - 提高吞吐量，减少状态访问频率
        config.setString("table.exec.mini-batch.enabled", "true");
        config.setString("table.exec.mini-batch.allow-latency", "1s");
        config.setString("table.exec.mini-batch.size", "1000");

        // 状态 TTL 优化 - 防止状态无限增长
        config.setString("table.exec.state.ttl", "1h");

        // Sink 优化 - 减少不必要的物化操作
        config.setString("table.exec.sink.not-null-enforcer", "drop");
        config.setString("table.exec.sink.upsert-materialize", "none");

        // 资源优化
        config.setString("table.exec.resource.default-parallelism", "4");

        // 时间语义优化
        config.setString("table.exec.emit.early-fire.enabled", "true");
        config.setString("table.exec.emit.early-fire.delay", "1s");
        
        // 检查点优化
        config.setString("execution.checkpointing.interval", "60s");
        config.setString("execution.checkpointing.timeout", "300s");
        config.setString("execution.checkpointing.max-concurrent-checkpoints", "1");
        
        // 内存管理优化
        config.setString("taskmanager.memory.managed.fraction", "0.4");
        config.setString("table.exec.spill-compression.enabled", "true");
        
        System.out.println("✅ Flink SQL TableEnvironment optimized successfully");
    }

    /**
     * 为高吞吐量场景优化 TableEnvironment
     * 
     * @param tableEnv Flink StreamTableEnvironment
     */
    public static void optimizeForHighThroughput(StreamTableEnvironment tableEnv) {
        Configuration config = tableEnv.getConfig().getConfiguration();
        
        // 增加 mini-batch 大小以提高吞吐量
        config.setString("table.exec.mini-batch.size", "5000");
        config.setString("table.exec.mini-batch.allow-latency", "5s");
        
        // 增加并行度
        config.setString("table.exec.resource.default-parallelism", "8");
        
        // 优化写入缓冲
        config.setString("table.exec.sink.buffer-flush.max-rows", "50000");
        config.setString("table.exec.sink.buffer-flush.interval", "10s");
        
        System.out.println("✅ Flink SQL TableEnvironment optimized for high throughput");
    }

    /**
     * 为低延迟场景优化 TableEnvironment
     * 
     * @param tableEnv Flink StreamTableEnvironment
     */
    public static void optimizeForLowLatency(StreamTableEnvironment tableEnv) {
        Configuration config = tableEnv.getConfig().getConfiguration();
        
        // 减少 mini-batch 延迟
        config.setString("table.exec.mini-batch.allow-latency", "100ms");
        config.setString("table.exec.mini-batch.size", "100");
        
        // 启用早期触发
        config.setString("table.exec.emit.early-fire.enabled", "true");
        config.setString("table.exec.emit.early-fire.delay", "100ms");
        
        // 优化写入频率
        config.setString("table.exec.sink.buffer-flush.max-rows", "1000");
        config.setString("table.exec.sink.buffer-flush.interval", "1s");
        
        System.out.println("✅ Flink SQL TableEnvironment optimized for low latency");
    }
}