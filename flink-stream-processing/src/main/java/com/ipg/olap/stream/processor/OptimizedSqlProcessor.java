package com.ipg.olap.stream.processor;

import org.apache.flink.configuration.Configuration;

/**
 * 优化的 SQL 处理器基类
 * 扩展 AbstractTableProcessor，支持不同处理器类型的优化配置
 */
public abstract class OptimizedSqlProcessor extends AbstractTableProcessor {

    /**
     * 处理器类型枚举，定义不同处理阶段的推荐并行度配置
     */
    public enum ProcessorType {
        DIMENSION("维度表处理", 2, "128mb", "24h"),
        FACT("事实表处理", 4, "512mb", "1h"),
        WIDE("宽表处理", 6, "1gb", "30m"),
        AGGREGATION("聚合处理", 8, "256mb", "2h"),
        KPI("KPI计算", 4, "256mb", "4h");

        private final String description;
        private final int recommendedParallelism;
        private final String writeBufferSize;
        private final String cacheTtl;

        ProcessorType(String description, int recommendedParallelism, String writeBufferSize, String cacheTtl) {
            this.description = description;
            this.recommendedParallelism = recommendedParallelism;
            this.writeBufferSize = writeBufferSize;
            this.cacheTtl = cacheTtl;
        }

        public String getDescription() {
            return description;
        }

        public int getRecommendedParallelism() {
            return recommendedParallelism;
        }

        public String getWriteBufferSize() {
            return writeBufferSize;
        }

        public String getCacheTtl() {
            return cacheTtl;
        }
    }

    protected OptimizedSqlProcessor(String processorName, String targetTableName) {
        super(processorName, targetTableName);
    }

    /**
     * 根据处理器类型获取优化的 Paimon 配置
     * 覆盖父类方法，提供基于处理器类型的优化配置
     */
    @Override
    protected Configuration getDefaultPaimonTableConfig() {
        Configuration config = new Configuration();
        ProcessorType type = getProcessorType();

        switch (type) {
            case DIMENSION:
                // 维度表：小数据量，高查询频率
                config.setString("write-buffer-size", "128mb");
                config.setString("sink.parallelism", "2");
                config.setString("lookup.cache.ttl", "24h");
                config.setString("compaction.min.file-num", "2");
                break;

            case FACT:
                // 事实表：中等数据量，批量写入
                config.setString("write-buffer-size", "512mb");
                config.setString("sink.parallelism", "4");
                config.setString("lookup.cache.ttl", "1h");
                config.setString("compaction.min.file-num", "5");
                break;

            case WIDE:
                // 宽表：大数据量，高吞吐
                config.setString("write-buffer-size", "512mb");
                config.setString("sink.parallelism", "8");
                config.setString("lookup.cache.ttl", "30m");
                config.setString("compaction.min.file-num", "10");
                break;

            case AGGREGATION:
                // 聚合表：复杂计算
                config.setString("write-buffer-size", "256mb");
                config.setString("sink.parallelism", "6");
                config.setString("lookup.cache.ttl", "2h");
                config.setString("compaction.min.file-num", "3");
                break;

            case KPI:
                // KPI表：最终输出
                config.setString("write-buffer-size", "256mb");
                config.setString("sink.parallelism", "4");
                config.setString("lookup.cache.ttl", "4h");
                config.setString("compaction.min.file-num", "2");
                break;
        }

        // 通用优化配置
        config.setString("changelog-producer", "lookup");
        config.setString("precommit-compact", "true");
        config.setString("compaction.max.file-num", "50");
        config.setString("snapshot.time-retained", "1h");
        // 注意：scan.snapshot-id 需要是具体的数字，不能是 "latest"

        return config;
    }

    /**
     * 获取优化的 SQL 执行提示
     * 注意：Flink SQL 不支持 SQL hints，此方法已废弃，返回空字符串
     * 所有优化配置应在表创建的 WITH 子句中指定
     * 
     * @return 空字符串（SQL hints 不被 Flink 支持）
     * @deprecated Flink SQL 不支持 SQL hints，请在表创建时使用 WITH 子句配置优化参数
     */
    @Deprecated
    protected String getOptimizedSqlHints() {
        // 返回空字符串，因为 Flink SQL 不支持 SQL hints
        // 所有优化配置应在表创建的 WITH 子句中指定
        return "";
    }

    /**
     * 获取处理器类型，子类必须实现
     * 
     * @return 处理器类型
     */
    protected abstract ProcessorType getProcessorType();

    /**
     * 获取处理器性能配置信息
     * 
     * @return 配置信息字符串
     */
    public String getPerformanceInfo() {
        ProcessorType type = getProcessorType();
        return String.format(
            "Processor: %s, Type: %s, Parallelism: %d, Buffer: %s, Cache TTL: %s",
            getProcessorName(),
            type.getDescription(),
            type.getRecommendedParallelism(),
            type.getWriteBufferSize(),
            type.getCacheTtl()
        );
    }
}