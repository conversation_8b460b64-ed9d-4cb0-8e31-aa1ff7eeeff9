package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 优化的应用宽表处理器
 * 
 * 优化策略：
 * 1. 简化 CTE 结构，减少嵌套层次
 * 2. 优化关联顺序，将小表放在右侧
 * 3. 使用更高效的时态关联模式
 * 4. 预计算常用字段
 * 
 * 保持业务逻辑完全一致，确保与 StarRocks 物化视图结果相同
 */
public class OptimizedApplicationWideProcessor extends OptimizedSqlProcessor {

    private static final Logger log = LoggerFactory.getLogger(OptimizedApplicationWideProcessor.class);

    public OptimizedApplicationWideProcessor() {
        super("OptimizedApplicationWideProcessor", "dwd_apn.dwd_application_wide");
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.WIDE;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "dwd_apn.dwd_application_fact",
            "dwd_apn.dwd_application_dimension", 
            "ods_apn.talent_recruitment_process_kpi_user",
            "ods_apn.user",
            "ods_apn.permission_user_team",
            "ods_apn.permission_team"
        };
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableDDL = """
            CREATE TABLE IF NOT EXISTS %s (
                tenant_id BIGINT,
                company_id BIGINT,
                job_id BIGINT,
                job_pteam_id BIGINT,
                talent_recruitment_process_id BIGINT,
                talent_id BIGINT,
                ai_score DOUBLE,
                recommend_feedback_id BIGINT,
                team_id BIGINT,
                team_name STRING,
                user_id BIGINT,
                user_name STRING,
                user_activated BOOLEAN,
                user_role INT,
                node_id BIGINT,
                node_type INT,
                node_status INT,
                progress INT,
                final_round BOOLEAN,
                add_date TIMESTAMP(3),
                event_date TIMESTAMP(3),
                last_modified_date TIMESTAMP(3),
                note_last_modify_date TIMESTAMP(3),
                dt STRING,
                PRIMARY KEY (talent_recruitment_process_id, user_id, node_id, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )""".formatted(targetTableName, getOptimizedPaimonTableConfig());

        tableEnv.executeSql(createTableDDL);
        
        log.info("✅ Optimized wide table created: {}", getTargetTableName());
        log.info("📈 Performance info: {}", getPerformanceInfo());
    }

    /**
     * 获取优化的 Paimon 表配置
     */
    private String getOptimizedPaimonTableConfig() {
        return String.join(",\n                ",
            "'write-buffer-size' = '512mb'",
            "'sink.parallelism' = '6'",
            "'lookup.cache.ttl' = '30m'",
            "'lookup.cache.max-rows' = '100000'",
            "'compaction.min.file-num' = '10'",
            "'changelog-producer' = 'lookup'",
            "'precommit-compact' = 'true'",
            "'compaction.max.file-num' = '50'",
            "'snapshot.time-retained' = '1h'",
            "'bucket' = '6'",
            "'file.target-file-size' = '128MB'"
        );
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        // 使用优化的宽表查询
        String optimizedWideSQL = buildOptimizedApplicationWideQuery();
        
        log.info("🚀 Executing optimized wide table processing SQL:");
        log.info("📊 Query uses optimized CTE and temporal joins");

        statementSet.addInsertSql(optimizedWideSQL);
    }

    /**
     * 构建优化的应用宽表查询
     * 
     * 优化点：
     * 1. 简化 CTE 结构，减少嵌套
     * 2. 优化关联顺序
     * 3. 使用更高效的时态关联
     * 4. 预计算分区字段
     */
    private String buildOptimizedApplicationWideQuery() {
        return String.format("""
            INSERT INTO %s
            -- 优化的宽表查询，保持与 StarRocks 物化视图完全一致的业务逻辑
            
            WITH fact_with_user AS (
                -- 从 KPI User 关联 - 优化版本
                SELECT 
                    fact.talent_recruitment_process_id,
                    fact.node_type,
                    fact.node_status,
                    fact.node_id,
                    fact.progress,
                    fact.final_round,
                    fact.add_date,
                    fact.event_date,
                    fact.last_modified_date,
                    fact.note_last_modify_date,
                    kpi.user_id,
                    CASE WHEN kpi.user_role IS NOT NULL THEN kpi.user_role ELSE 10000 END AS user_role,
                    DATE_FORMAT(fact.add_date, 'yyyy-MM') AS dt
                FROM (
                    SELECT *, PROCTIME() AS proc_time 
                    FROM dwd_apn.dwd_application_fact
                ) AS fact
                INNER JOIN ods_apn.talent_recruitment_process_kpi_user FOR SYSTEM_TIME AS OF fact.proc_time AS kpi
                    ON fact.talent_recruitment_process_id = kpi.talent_recruitment_process_id

                UNION ALL

                -- 从 Operator 补充 - 优化版本
                SELECT 
                    fact.talent_recruitment_process_id,
                    fact.node_type,
                    fact.node_status,
                    fact.node_id,
                    fact.progress,
                    fact.final_round,
                    fact.add_date,
                    fact.event_date,
                    fact.last_modified_date,
                    fact.note_last_modify_date,
                    fact.operator AS user_id,
                    10 AS user_role,
                    DATE_FORMAT(fact.add_date, 'yyyy-MM') AS dt
                FROM dwd_apn.dwd_application_fact AS fact
            )
            -- 主查询 - 优化关联顺序和时态关联
            SELECT 
                dim.tenant_id,
                dim.company_id,
                dim.job_id,
                dim.job_pteam_id,
                dim.talent_recruitment_process_id,
                dim.talent_id,
                dim.ai_score,
                dim.recommend_feedback_id,
                pt.id AS team_id,
                pt.name AS team_name,
                fact_with_user.user_id,
                CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) AS user_name,
                u.activated AS user_activated,
                fact_with_user.user_role,
                fact_with_user.node_id,
                fact_with_user.node_type,
                fact_with_user.node_status,
                fact_with_user.progress,
                fact_with_user.final_round,
                fact_with_user.add_date,
                fact_with_user.event_date,
                fact_with_user.last_modified_date,
                fact_with_user.note_last_modify_date,
                fact_with_user.dt
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM fact_with_user
            ) AS fact_with_user
            -- 优化关联顺序：先关联维度表（通常较小）
            LEFT JOIN dwd_apn.dwd_application_dimension FOR SYSTEM_TIME AS OF fact_with_user.proc_time AS dim
                ON fact_with_user.talent_recruitment_process_id = dim.talent_recruitment_process_id
                AND fact_with_user.dt = dim.dt
            -- 然后关联用户表
            LEFT JOIN ods_apn.user FOR SYSTEM_TIME AS OF fact_with_user.proc_time AS u
                ON fact_with_user.user_id = u.id 
                AND dim.tenant_id = u.tenant_id
            -- 关联用户团队表
            LEFT JOIN ods_apn.permission_user_team FOR SYSTEM_TIME AS OF fact_with_user.proc_time AS put
                ON u.id = put.user_id 
                AND put.is_primary = 1
            -- 最后关联团队表
            LEFT JOIN ods_apn.permission_team FOR SYSTEM_TIME AS OF fact_with_user.proc_time AS pt
                ON put.team_id = pt.id
            """, targetTableName);
    }

    @Override
    public String getPerformanceInfo() {
        return super.getPerformanceInfo() + 
               ", Optimizations: Simplified CTE, Optimized join order, Efficient temporal joins, Pre-computed partitions";
    }
}
