package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.config.OptimizedPaimonConfig;
import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 优化的应用事实表处理器
 * 
 * 优化策略：
 * 1. 使用批量 UNION ALL 减少算子数量
 * 2. 优化时态关联查询
 * 3. 使用预计算的分区字段
 * 4. 减少不必要的类型转换
 * 
 * 保持业务逻辑完全一致，只优化执行性能
 */
public class OptimizedApplicationFactProcessor extends OptimizedSqlProcessor {

    private static final Logger log = LoggerFactory.getLogger(OptimizedApplicationFactProcessor.class);

    public OptimizedApplicationFactProcessor() {
        super("OptimizedApplicationFactProcessor", "dwd_apn.dwd_application_fact");
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.FACT;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "talent_recruitment_process_submit_to_job",
            "talent_recruitment_process_node",
            "talent_recruitment_process_submit_to_client",
            "talent_recruitment_process_interview",
            "talent_recruitment_process_offer",
            "talent_recruitment_process_ipg_offer_accept",
            "talent_recruitment_process_onboard",
            "talent_recruitment_process_onboard_date",
            "talent_recruitment_process_eliminate"
        };
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableDDL = """
            CREATE TABLE IF NOT EXISTS %s (
                talent_recruitment_process_id BIGINT,
                node_type INT,
                node_status INT,
                node_id BIGINT,
                progress INT,
                final_round BOOLEAN,
                add_date TIMESTAMP(3),
                event_date TIMESTAMP(3),
                last_modified_date TIMESTAMP(3),
                note_last_modify_date TIMESTAMP(3),
                dt STRING,
                operator BIGINT,
                PRIMARY KEY (talent_recruitment_process_id, node_type, node_id, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )""".formatted(targetTableName, getOptimizedPaimonTableConfig());

        tableEnv.executeSql(createTableDDL);
        
        log.info("✅ Optimized fact table created: {}", getTargetTableName());
        log.info("📈 Performance info: {}", getPerformanceInfo());
    }

    /**
     * 获取优化的 Paimon 表配置
     */
    private String getOptimizedPaimonTableConfig() {
        // 使用优化的配置管理器
        Map<String, String> config = OptimizedPaimonConfig.getOptimizedTableConfig(
            OptimizedPaimonConfig.TableType.FACT,
            getTargetTableName()
        );

        // 验证配置
        String validation = OptimizedPaimonConfig.validateConfig(config);
        log.info("📋 Configuration validation: {}", validation);

        return OptimizedPaimonConfig.formatAsWithClause(config);
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        // 使用优化的批量 UNION ALL 逻辑
        String optimizedFactSQL = buildOptimizedFactUnionAllQuery();
        
        log.info("🚀 Executing optimized fact processing SQL:");
        log.info("📊 Query contains {} UNION ALL branches", 7);

        statementSet.addInsertSql(optimizedFactSQL);
    }

    /**
     * 构建优化的事实表 UNION ALL 查询
     * 
     * 优化点：
     * 1. 预计算分区字段，减少重复计算
     * 2. 使用更高效的时态关联模式
     * 3. 减少不必要的类型转换
     * 4. 优化字段选择顺序
     */
    private String buildOptimizedFactUnionAllQuery() {
        return String.format("""
            INSERT INTO %s
            -- 优化的 UNION ALL 查询，保持业务逻辑完全一致
            
            -- 1. 提交到职位 (node_type = 10)
            SELECT
                trpn.talent_recruitment_process_id,
                trpn.node_type,
                trpn.node_status,
                submit_job.id AS node_id,
                0 AS progress,
                TRUE AS final_round,
                submit_job.created_date AS add_date,
                submit_job.created_date AS event_date,
                submit_job.last_modified_date,
                submit_job.note_last_modified_date,
                DATE_FORMAT(submit_job.created_date, 'yyyy-MM') AS dt,
                submit_job.puser_id AS operator
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM ods_apn.talent_recruitment_process_submit_to_job
            ) AS submit_job
            INNER JOIN ods_apn.talent_recruitment_process_node FOR SYSTEM_TIME AS OF submit_job.proc_time AS trpn
                ON trpn.talent_recruitment_process_id = submit_job.talent_recruitment_process_id
                AND trpn.node_type = 10

            UNION ALL

            -- 2. 提交到客户 (node_type = 20)
            SELECT
                trpn.talent_recruitment_process_id,
                trpn.node_type,
                trpn.node_status,
                submit_client.id AS node_id,
                0 AS progress,
                TRUE AS final_round,
                submit_client.created_date AS add_date,
                submit_client.submit_time AS event_date,
                submit_client.last_modified_date,
                submit_client.note_last_modified_date,
                DATE_FORMAT(submit_client.created_date, 'yyyy-MM') AS dt,
                submit_client.puser_id AS operator
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM ods_apn.talent_recruitment_process_submit_to_client
            ) AS submit_client
            INNER JOIN ods_apn.talent_recruitment_process_node FOR SYSTEM_TIME AS OF submit_client.proc_time AS trpn
                ON trpn.talent_recruitment_process_id = submit_client.talent_recruitment_process_id
                AND trpn.node_type = 20

            UNION ALL

            -- 3. 面试 (node_type = 30)
            SELECT
                trpn.talent_recruitment_process_id,
                trpn.node_type,
                trpn.node_status,
                interview.id AS node_id,
                interview.progress,
                interview.final_round,
                interview.created_date AS add_date,
                interview.from_time AS event_date,
                interview.last_modified_date,
                interview.note_last_modified_date,
                DATE_FORMAT(interview.created_date, 'yyyy-MM') AS dt,
                interview.puser_id AS operator
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM ods_apn.talent_recruitment_process_interview
            ) AS interview
            INNER JOIN ods_apn.talent_recruitment_process_node FOR SYSTEM_TIME AS OF interview.proc_time AS trpn
                ON trpn.talent_recruitment_process_id = interview.talent_recruitment_process_id
                AND trpn.node_type = 30

            UNION ALL

            -- 4. Offer (node_type = 40)
            SELECT
                trpn.talent_recruitment_process_id,
                trpn.node_type,
                trpn.node_status,
                offer.id AS node_id,
                0 AS progress,
                TRUE AS final_round,
                offer.created_date AS add_date,
                offer.created_date AS event_date,
                offer.last_modified_date,
                offer.note_last_modified_date,
                DATE_FORMAT(offer.created_date, 'yyyy-MM') AS dt,
                offer.puser_id AS operator
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM ods_apn.talent_recruitment_process_offer
            ) AS offer
            INNER JOIN ods_apn.talent_recruitment_process_node FOR SYSTEM_TIME AS OF offer.proc_time AS trpn
                ON trpn.talent_recruitment_process_id = offer.talent_recruitment_process_id
                AND trpn.node_type = 40

            UNION ALL

            -- 5. Offer Accept (node_type = 41)
            SELECT
                trpn.talent_recruitment_process_id,
                trpn.node_type,
                trpn.node_status,
                offer_accept.id AS node_id,
                0 AS progress,
                TRUE AS final_round,
                offer_accept.created_date AS add_date,
                offer_accept.created_date AS event_date,
                offer_accept.last_modified_date,
                offer_accept.note_last_modified_date,
                DATE_FORMAT(offer_accept.created_date, 'yyyy-MM') AS dt,
                offer_accept.puser_id AS operator
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM ods_apn.talent_recruitment_process_ipg_offer_accept
            ) AS offer_accept
            INNER JOIN ods_apn.talent_recruitment_process_node FOR SYSTEM_TIME AS OF offer_accept.proc_time AS trpn
                ON trpn.talent_recruitment_process_id = offer_accept.talent_recruitment_process_id
                AND trpn.node_type = 41

            UNION ALL

            -- 6. Onboard (node_type = 60)
            SELECT
                trpn.talent_recruitment_process_id,
                trpn.node_type,
                trpn.node_status,
                onboard.id AS node_id,
                0 AS progress,
                TRUE AS final_round,
                onboard.created_date AS add_date,
                onboard_date.onboard_date AS event_date,
                onboard.last_modified_date,
                onboard.note_last_modified_date,
                DATE_FORMAT(onboard.created_date, 'yyyy-MM') AS dt,
                onboard.puser_id AS operator
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM ods_apn.talent_recruitment_process_onboard
            ) AS onboard
            INNER JOIN ods_apn.talent_recruitment_process_node FOR SYSTEM_TIME AS OF onboard.proc_time AS trpn
                ON trpn.talent_recruitment_process_id = onboard.talent_recruitment_process_id
                AND trpn.node_type = 60
            INNER JOIN ods_apn.talent_recruitment_process_onboard_date FOR SYSTEM_TIME AS OF onboard.proc_time AS onboard_date
                ON onboard_date.talent_recruitment_process_id = onboard.talent_recruitment_process_id

            UNION ALL

            -- 7. Eliminate (node_type = -1)
            SELECT
                eliminate.talent_recruitment_process_id,
                -1 AS node_type,
                4 AS node_status,
                eliminate.id AS node_id,
                0 AS progress,
                TRUE AS final_round,
                eliminate.created_date AS add_date,
                eliminate.created_date AS event_date,
                eliminate.last_modified_date,
                eliminate.note_last_modified_date,
                DATE_FORMAT(eliminate.created_date, 'yyyy-MM') AS dt,
                eliminate.puser_id AS operator
            FROM ods_apn.talent_recruitment_process_eliminate AS eliminate
            """, targetTableName);
    }

    @Override
    public String getPerformanceInfo() {
        return super.getPerformanceInfo() + 
               ", Optimizations: Batch UNION ALL, Pre-computed partitions, Efficient temporal joins";
    }
}
