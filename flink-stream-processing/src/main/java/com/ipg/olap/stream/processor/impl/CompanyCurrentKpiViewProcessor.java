package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import com.ipg.olap.stream.optimizer.SqlQueryOptimizer;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * CompanyCurrentKpiViewProcessor - 实现公司级当前 KPI 视图处理器
 * 
 * 实现 view_company_application_current_kpi 的完整逻辑，包括：
 * - 实现公司级聚合逻辑简化团队层级复杂性
 * - 优化面向公司特定报告和分析用例的查询性能
 * - 创建公司级别的 KPI 聚合，不包含团队层级关联
 * 
 * Requirements: 4.4
 */
public class CompanyCurrentKpiViewProcessor extends OptimizedSqlProcessor {

    private static final Logger log = LoggerFactory.getLogger(CompanyCurrentKpiViewProcessor.class);

    public CompanyCurrentKpiViewProcessor() {
        super("CompanyCurrentKpiViewProcessor", "view_company_application_current_kpi");
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.AGGREGATION;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "mv_application_to_job",
            "mv_application_to_client", 
            "mv_application_interview",
            "mv_application_reserve_interview",
            "mv_application_offer",
            "mv_application_offer_accept",
            "mv_application_onboard",
            "mv_application_emiminate"
        };
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        try {
            log.info("开始处理公司级当前 KPI 视图聚合: {}", getProcessorName());

            // 构建并执行 UNION ALL 查询
            String unionAllQuery = buildCompanyCurrentKpiUnionAllQuery();
            String optimizedQuery = "INSERT INTO " + getTargetTableName() + "\n" + unionAllQuery;

            log.info("执行公司级当前 KPI 视图查询，预计处理 {} 个数据源", 8);
            statementSet.addInsertSql(optimizedQuery);

            log.info("公司级当前 KPI 视图处理完成: {}", getProcessorName());

        } catch (Exception e) {
            log.error("公司级当前 KPI 视图处理失败: {}", getProcessorName(), e);
            throw new RuntimeException("CompanyCurrentKpiViewProcessor 处理失败", e);
        }
    }

    /**
     * 创建公司级当前 KPI 视图目标表
     * 注意：公司级视图不包含团队层级字段，简化了数据结构
     */
    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableSql = """
            CREATE TABLE IF NOT EXISTS %s (
                tenant_id BIGINT,
                company_id BIGINT,
                job_id BIGINT,
                job_pteam_id BIGINT,
                team_id BIGINT,
                team_name STRING,
                user_id BIGINT,
                user_name STRING,
                user_activated BOOLEAN,
                add_date TIMESTAMP(3),
                event_date TIMESTAMP(3),
                user_roles MULTISET<INT>,
                submit_to_job_current_countNum MULTISET<BIGINT>,
                submit_to_job_currentAiRecommendNum MULTISET<BIGINT>,
                submit_to_job_currentPrecisionAiRecommendNum MULTISET<BIGINT>,
                submit_to_job_currentStayedOver MULTISET<BIGINT>,
                submit_to_client_current_countNum MULTISET<BIGINT>,
                submit_to_client_currentAiRecommendNum MULTISET<BIGINT>,
                submit_to_client_currentPrecisionAiRecommendNum MULTISET<BIGINT>,
                submit_to_client_currentStayedOver MULTISET<BIGINT>,
                current_interview1 MULTISET<BIGINT>,
                current_interview2 MULTISET<BIGINT>,
                current_two_or_more_interviews MULTISET<BIGINT>,
                current_interview_final MULTISET<BIGINT>,
                current_interview_total MULTISET<BIGINT>,
                unique_interview_talents MULTISET<BIGINT>,
                current_interview_total_process MULTISET<BIGINT>,
                currentInterviewTotalAiRecommendNum MULTISET<BIGINT>,
                currentInterviewTotalProcessAiRecommendNum MULTISET<BIGINT>,
                currentInterview1AiRecommendNum MULTISET<BIGINT>,
                currentInterview2AiRecommendNum MULTISET<BIGINT>,
                currentTwoOrMoreInterviewsAiRecommendNum MULTISET<BIGINT>,
                currentInterviewFinalAiRecommendNum MULTISET<BIGINT>,
                currentInterviewTotalPrecisionAiRecommendNum MULTISET<BIGINT>,
                currentInterviewNumProcessPrecisionAIRecommend MULTISET<BIGINT>,
                currentInterview1PrecisionAiRecommendNum MULTISET<BIGINT>,
                currentInterview2PrecisionAiRecommendNum MULTISET<BIGINT>,
                currentTwoOrMoreInterviewsPrecisionAiRecommendNum MULTISET<BIGINT>,
                currentInterviewFinalPrecisionAiRecommendNum MULTISET<BIGINT>,
                reserve_current_interview_total MULTISET<BIGINT>,
                reserve_interview_currentAiRecommendNum MULTISET<BIGINT>,
                reserve_interview_currentPrecisionAiRecommendNum MULTISET<BIGINT>,
                offer_current_countNum MULTISET<BIGINT>,
                offer_currentAiRecommendNum MULTISET<BIGINT>,
                offer_currentPrecisionAiRecommendNum MULTISET<BIGINT>,
                offer_accept_current_countNum MULTISET<BIGINT>,
                offer_accept_currentAiRecommendNum MULTISET<BIGINT>,
                offer_accept_currentPrecisionAiRecommendNum MULTISET<BIGINT>,
                onboard_current_countNum MULTISET<BIGINT>,
                onboard_currentAiRecommendNum MULTISET<BIGINT>,
                onboard_currentPrecisionAiRecommendNum MULTISET<BIGINT>,
                eliminate_current_countNum MULTISET<BIGINT>,
                eliminate_currentAiRecommendNum MULTISET<BIGINT>,
                eliminate_currentPrecisionAiRecommendNum MULTISET<BIGINT>,
                dt STRING,
                PRIMARY KEY (tenant_id, company_id, job_id, team_id, user_id, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )
            """.formatted(getTargetTableName(), getOptimizedPaimonTableConfig());

        tableEnv.executeSql(createTableSql);
        log.info("创建公司级当前 KPI 视图目标表: {}", getTargetTableName());
    }

    /**
     * 获取优化的 Paimon 表配置字符串
     * 基于聚合表的特性进行优化配置，公司级视图优化
     */
    private String getOptimizedPaimonTableConfig() {
        return String.join(",\n                ",
            "'write-buffer-size' = '256mb'",
            "'sink.parallelism' = '6'", 
            "'lookup.cache.ttl' = '2h'",
            "'compaction.min.file-num' = '3'",
            "'changelog-producer' = 'lookup'",
            "'precommit-compact' = 'true'",
            "'compaction.max.file-num' = '50'",
            "'snapshot.time-retained' = '1h'",
            "'bucket' = '6'",
            "'file.target-file-size' = '64MB'"
        );
    }

    /**
     * 构建公司级当前 KPI 的 UNION ALL 查询
     * 注意：公司级视图直接聚合，不需要团队层级关联
     */
    private String buildCompanyCurrentKpiUnionAllQuery() {
        // 构建各个阶段的子查询，重用现有的子查询但去掉团队层级关联
        List<String> subQueries = Arrays.asList(
            buildCompanyToJobSubQuery(),
            buildCompanyToClientSubQuery(),
            buildCompanyInterviewSubQuery(),
            buildCompanyReserveInterviewSubQuery(),
            buildCompanyOfferSubQuery(),
            buildCompanyOfferAcceptSubQuery(),
            buildCompanyOnboardSubQuery(),
            buildCompanyEliminateSubQuery()
        );

        // 使用 SqlQueryOptimizer 构建优化的 UNION ALL
        String unionQuery = SqlQueryOptimizer.buildOptimizedUnionAll(subQueries);

        // 直接聚合，不需要团队层级关联
        return """
            SELECT 
                base.tenant_id,
                base.company_id,
                base.job_id,
                base.job_pteam_id,
                base.team_id,
                base.team_name,
                base.user_id,
                base.user_name,
                base.user_activated,
                base.add_date,
                base.event_date,
                ARRAY_UNION_AGG(base.user_roles) AS user_roles,
                ARRAY_UNION_AGG(base.submit_to_job_current_countNum) AS submit_to_job_current_countNum,
                ARRAY_UNION_AGG(base.submit_to_job_currentAiRecommendNum) AS submit_to_job_currentAiRecommendNum,
                ARRAY_UNION_AGG(base.submit_to_job_currentPrecisionAiRecommendNum) AS submit_to_job_currentPrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.submit_to_job_currentStayedOver) AS submit_to_job_currentStayedOver,
                ARRAY_UNION_AGG(base.submit_to_client_current_countNum) AS submit_to_client_current_countNum,
                ARRAY_UNION_AGG(base.submit_to_client_currentAiRecommendNum) AS submit_to_client_currentAiRecommendNum,
                ARRAY_UNION_AGG(base.submit_to_client_currentPrecisionAiRecommendNum) AS submit_to_client_currentPrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.submit_to_client_currentStayedOver) AS submit_to_client_currentStayedOver,
                ARRAY_UNION_AGG(base.current_interview1) AS current_interview1,
                ARRAY_UNION_AGG(base.current_interview2) AS current_interview2,
                ARRAY_UNION_AGG(base.current_two_or_more_interviews) AS current_two_or_more_interviews,
                ARRAY_UNION_AGG(base.current_interview_final) AS current_interview_final,
                ARRAY_UNION_AGG(base.current_interview_total) AS current_interview_total,
                ARRAY_UNION_AGG(base.unique_interview_talents) AS unique_interview_talents,
                ARRAY_UNION_AGG(base.current_interview_total_process) AS current_interview_total_process,
                ARRAY_UNION_AGG(base.currentInterviewTotalAiRecommendNum) AS currentInterviewTotalAiRecommendNum,
                ARRAY_UNION_AGG(base.currentInterviewTotalProcessAiRecommendNum) AS currentInterviewTotalProcessAiRecommendNum,
                ARRAY_UNION_AGG(base.currentInterview1AiRecommendNum) AS currentInterview1AiRecommendNum,
                ARRAY_UNION_AGG(base.currentInterview2AiRecommendNum) AS currentInterview2AiRecommendNum,
                ARRAY_UNION_AGG(base.currentTwoOrMoreInterviewsAiRecommendNum) AS currentTwoOrMoreInterviewsAiRecommendNum,
                ARRAY_UNION_AGG(base.currentInterviewFinalAiRecommendNum) AS currentInterviewFinalAiRecommendNum,
                ARRAY_UNION_AGG(base.currentInterviewTotalPrecisionAiRecommendNum) AS currentInterviewTotalPrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.currentInterviewNumProcessPrecisionAIRecommend) AS currentInterviewNumProcessPrecisionAIRecommend,
                ARRAY_UNION_AGG(base.currentInterview1PrecisionAiRecommendNum) AS currentInterview1PrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.currentInterview2PrecisionAiRecommendNum) AS currentInterview2PrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.currentTwoOrMoreInterviewsPrecisionAiRecommendNum) AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.currentInterviewFinalPrecisionAiRecommendNum) AS currentInterviewFinalPrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.reserve_current_interview_total) AS reserve_current_interview_total,
                ARRAY_UNION_AGG(base.reserve_interview_currentAiRecommendNum) AS reserve_interview_currentAiRecommendNum,
                ARRAY_UNION_AGG(base.reserve_interview_currentPrecisionAiRecommendNum) AS reserve_interview_currentPrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.offer_current_countNum) AS offer_current_countNum,
                ARRAY_UNION_AGG(base.offer_currentAiRecommendNum) AS offer_currentAiRecommendNum,
                ARRAY_UNION_AGG(base.offer_currentPrecisionAiRecommendNum) AS offer_currentPrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.offer_accept_current_countNum) AS offer_accept_current_countNum,
                ARRAY_UNION_AGG(base.offer_accept_currentAiRecommendNum) AS offer_accept_currentAiRecommendNum,
                ARRAY_UNION_AGG(base.offer_accept_currentPrecisionAiRecommendNum) AS offer_accept_currentPrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.onboard_current_countNum) AS onboard_current_countNum,
                ARRAY_UNION_AGG(base.onboard_currentAiRecommendNum) AS onboard_currentAiRecommendNum,
                ARRAY_UNION_AGG(base.onboard_currentPrecisionAiRecommendNum) AS onboard_currentPrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.eliminate_current_countNum) AS eliminate_current_countNum,
                ARRAY_UNION_AGG(base.eliminate_currentAiRecommendNum) AS eliminate_currentAiRecommendNum,
                ARRAY_UNION_AGG(base.eliminate_currentPrecisionAiRecommendNum) AS eliminate_currentPrecisionAiRecommendNum,
                base.dt
            FROM (
                %s
            ) AS base
            GROUP BY 
                base.tenant_id, base.company_id, base.job_id, base.job_pteam_id,
                base.team_id, base.team_name,
                base.user_id, base.user_name, base.user_activated,
                base.add_date, base.event_date, base.dt
            """.formatted(unionQuery);
    }

    // 公司级子查询方法 - 直接从源表查询，不需要团队层级关联
    private String buildCompanyToJobSubQuery() {
        return "SELECT tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated, add_date, event_date, user_roles, submit_to_job_current_countNum, submit_to_job_currentAiRecommendNum, submit_to_job_currentPrecisionAiRecommendNum, submit_to_job_currentStayedOver, ARRAY[] AS submit_to_client_current_countNum, ARRAY[] AS submit_to_client_currentAiRecommendNum, ARRAY[] AS submit_to_client_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_client_currentStayedOver, ARRAY[] AS current_interview1, ARRAY[] AS current_interview2, ARRAY[] AS current_two_or_more_interviews, ARRAY[] AS current_interview_final, ARRAY[] AS current_interview_total, ARRAY[] AS unique_interview_talents, ARRAY[] AS current_interview_total_process, ARRAY[] AS currentInterviewTotalAiRecommendNum, ARRAY[] AS currentInterviewTotalProcessAiRecommendNum, ARRAY[] AS currentInterview1AiRecommendNum, ARRAY[] AS currentInterview2AiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsAiRecommendNum, ARRAY[] AS currentInterviewFinalAiRecommendNum, ARRAY[] AS currentInterviewTotalPrecisionAiRecommendNum, ARRAY[] AS currentInterviewNumProcessPrecisionAIRecommend, ARRAY[] AS currentInterview1PrecisionAiRecommendNum, ARRAY[] AS currentInterview2PrecisionAiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum, ARRAY[] AS currentInterviewFinalPrecisionAiRecommendNum, ARRAY[] AS reserve_current_interview_total, ARRAY[] AS reserve_interview_currentAiRecommendNum, ARRAY[] AS reserve_interview_currentPrecisionAiRecommendNum, ARRAY[] AS offer_current_countNum, ARRAY[] AS offer_currentAiRecommendNum, ARRAY[] AS offer_currentPrecisionAiRecommendNum, ARRAY[] AS offer_accept_current_countNum, ARRAY[] AS offer_accept_currentAiRecommendNum, ARRAY[] AS offer_accept_currentPrecisionAiRecommendNum, ARRAY[] AS onboard_current_countNum, ARRAY[] AS onboard_currentAiRecommendNum, ARRAY[] AS onboard_currentPrecisionAiRecommendNum, ARRAY[] AS eliminate_current_countNum, ARRAY[] AS eliminate_currentAiRecommendNum, ARRAY[] AS eliminate_currentPrecisionAiRecommendNum, dt FROM mv_application_to_job";
    }

    private String buildCompanyToClientSubQuery() {
        return "SELECT tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated, add_date, event_date, user_roles, ARRAY[] AS submit_to_job_current_countNum, ARRAY[] AS submit_to_job_currentAiRecommendNum, ARRAY[] AS submit_to_job_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_job_currentStayedOver, submit_to_client_current_countNum, submit_to_client_currentAiRecommendNum, submit_to_client_currentPrecisionAiRecommendNum, submit_to_client_currentStayedOver, ARRAY[] AS current_interview1, ARRAY[] AS current_interview2, ARRAY[] AS current_two_or_more_interviews, ARRAY[] AS current_interview_final, ARRAY[] AS current_interview_total, ARRAY[] AS unique_interview_talents, ARRAY[] AS current_interview_total_process, ARRAY[] AS currentInterviewTotalAiRecommendNum, ARRAY[] AS currentInterviewTotalProcessAiRecommendNum, ARRAY[] AS currentInterview1AiRecommendNum, ARRAY[] AS currentInterview2AiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsAiRecommendNum, ARRAY[] AS currentInterviewFinalAiRecommendNum, ARRAY[] AS currentInterviewTotalPrecisionAiRecommendNum, ARRAY[] AS currentInterviewNumProcessPrecisionAIRecommend, ARRAY[] AS currentInterview1PrecisionAiRecommendNum, ARRAY[] AS currentInterview2PrecisionAiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum, ARRAY[] AS currentInterviewFinalPrecisionAiRecommendNum, ARRAY[] AS reserve_current_interview_total, ARRAY[] AS reserve_interview_currentAiRecommendNum, ARRAY[] AS reserve_interview_currentPrecisionAiRecommendNum, ARRAY[] AS offer_current_countNum, ARRAY[] AS offer_currentAiRecommendNum, ARRAY[] AS offer_currentPrecisionAiRecommendNum, ARRAY[] AS offer_accept_current_countNum, ARRAY[] AS offer_accept_currentAiRecommendNum, ARRAY[] AS offer_accept_currentPrecisionAiRecommendNum, ARRAY[] AS onboard_current_countNum, ARRAY[] AS onboard_currentAiRecommendNum, ARRAY[] AS onboard_currentPrecisionAiRecommendNum, ARRAY[] AS eliminate_current_countNum, ARRAY[] AS eliminate_currentAiRecommendNum, ARRAY[] AS eliminate_currentPrecisionAiRecommendNum, dt FROM mv_application_to_client";
    }

    private String buildCompanyInterviewSubQuery() {
        return "SELECT tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated, add_date, event_date, user_roles, ARRAY[] AS submit_to_job_current_countNum, ARRAY[] AS submit_to_job_currentAiRecommendNum, ARRAY[] AS submit_to_job_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_job_currentStayedOver, ARRAY[] AS submit_to_client_current_countNum, ARRAY[] AS submit_to_client_currentAiRecommendNum, ARRAY[] AS submit_to_client_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_client_currentStayedOver, current_interview1, current_interview2, current_two_or_more_interviews, current_interview_final, current_interview_total, unique_interview_talents, current_interview_total_process, currentInterviewTotalAiRecommendNum, currentInterviewTotalProcessAiRecommendNum, currentInterview1AiRecommendNum, currentInterview2AiRecommendNum, currentTwoOrMoreInterviewsAiRecommendNum, currentInterviewFinalAiRecommendNum, currentInterviewTotalPrecisionAiRecommendNum, currentInterviewNumProcessPrecisionAIRecommend, currentInterview1PrecisionAiRecommendNum, currentInterview2PrecisionAiRecommendNum, currentTwoOrMoreInterviewsPrecisionAiRecommendNum, currentInterviewFinalPrecisionAiRecommendNum, ARRAY[] AS reserve_current_interview_total, ARRAY[] AS reserve_interview_currentAiRecommendNum, ARRAY[] AS reserve_interview_currentPrecisionAiRecommendNum, ARRAY[] AS offer_current_countNum, ARRAY[] AS offer_currentAiRecommendNum, ARRAY[] AS offer_currentPrecisionAiRecommendNum, ARRAY[] AS offer_accept_current_countNum, ARRAY[] AS offer_accept_currentAiRecommendNum, ARRAY[] AS offer_accept_currentPrecisionAiRecommendNum, ARRAY[] AS onboard_current_countNum, ARRAY[] AS onboard_currentAiRecommendNum, ARRAY[] AS onboard_currentPrecisionAiRecommendNum, ARRAY[] AS eliminate_current_countNum, ARRAY[] AS eliminate_currentAiRecommendNum, ARRAY[] AS eliminate_currentPrecisionAiRecommendNum, dt FROM mv_application_interview";
    }

    private String buildCompanyReserveInterviewSubQuery() {
        return "SELECT tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated, add_date, event_date, user_roles, ARRAY[] AS submit_to_job_current_countNum, ARRAY[] AS submit_to_job_currentAiRecommendNum, ARRAY[] AS submit_to_job_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_job_currentStayedOver, ARRAY[] AS submit_to_client_current_countNum, ARRAY[] AS submit_to_client_currentAiRecommendNum, ARRAY[] AS submit_to_client_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_client_currentStayedOver, ARRAY[] AS current_interview1, ARRAY[] AS current_interview2, ARRAY[] AS current_two_or_more_interviews, ARRAY[] AS current_interview_final, ARRAY[] AS current_interview_total, ARRAY[] AS unique_interview_talents, ARRAY[] AS current_interview_total_process, ARRAY[] AS currentInterviewTotalAiRecommendNum, ARRAY[] AS currentInterviewTotalProcessAiRecommendNum, ARRAY[] AS currentInterview1AiRecommendNum, ARRAY[] AS currentInterview2AiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsAiRecommendNum, ARRAY[] AS currentInterviewFinalAiRecommendNum, ARRAY[] AS currentInterviewTotalPrecisionAiRecommendNum, ARRAY[] AS currentInterviewNumProcessPrecisionAIRecommend, ARRAY[] AS currentInterview1PrecisionAiRecommendNum, ARRAY[] AS currentInterview2PrecisionAiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum, ARRAY[] AS currentInterviewFinalPrecisionAiRecommendNum, reserve_current_interview_total, reserve_interview_currentAiRecommendNum, reserve_interview_currentPrecisionAiRecommendNum, ARRAY[] AS offer_current_countNum, ARRAY[] AS offer_currentAiRecommendNum, ARRAY[] AS offer_currentPrecisionAiRecommendNum, ARRAY[] AS offer_accept_current_countNum, ARRAY[] AS offer_accept_currentAiRecommendNum, ARRAY[] AS offer_accept_currentPrecisionAiRecommendNum, ARRAY[] AS onboard_current_countNum, ARRAY[] AS onboard_currentAiRecommendNum, ARRAY[] AS onboard_currentPrecisionAiRecommendNum, ARRAY[] AS eliminate_current_countNum, ARRAY[] AS eliminate_currentAiRecommendNum, ARRAY[] AS eliminate_currentPrecisionAiRecommendNum, dt FROM mv_application_reserve_interview";
    }

    private String buildCompanyOfferSubQuery() {
        return "SELECT tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated, add_date, event_date, user_roles, ARRAY[] AS submit_to_job_current_countNum, ARRAY[] AS submit_to_job_currentAiRecommendNum, ARRAY[] AS submit_to_job_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_job_currentStayedOver, ARRAY[] AS submit_to_client_current_countNum, ARRAY[] AS submit_to_client_currentAiRecommendNum, ARRAY[] AS submit_to_client_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_client_currentStayedOver, ARRAY[] AS current_interview1, ARRAY[] AS current_interview2, ARRAY[] AS current_two_or_more_interviews, ARRAY[] AS current_interview_final, ARRAY[] AS current_interview_total, ARRAY[] AS unique_interview_talents, ARRAY[] AS current_interview_total_process, ARRAY[] AS currentInterviewTotalAiRecommendNum, ARRAY[] AS currentInterviewTotalProcessAiRecommendNum, ARRAY[] AS currentInterview1AiRecommendNum, ARRAY[] AS currentInterview2AiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsAiRecommendNum, ARRAY[] AS currentInterviewFinalAiRecommendNum, ARRAY[] AS currentInterviewTotalPrecisionAiRecommendNum, ARRAY[] AS currentInterviewNumProcessPrecisionAIRecommend, ARRAY[] AS currentInterview1PrecisionAiRecommendNum, ARRAY[] AS currentInterview2PrecisionAiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum, ARRAY[] AS currentInterviewFinalPrecisionAiRecommendNum, ARRAY[] AS reserve_current_interview_total, ARRAY[] AS reserve_interview_currentAiRecommendNum, ARRAY[] AS reserve_interview_currentPrecisionAiRecommendNum, offer_current_countNum, offer_currentAiRecommendNum, offer_currentPrecisionAiRecommendNum, ARRAY[] AS offer_accept_current_countNum, ARRAY[] AS offer_accept_currentAiRecommendNum, ARRAY[] AS offer_accept_currentPrecisionAiRecommendNum, ARRAY[] AS onboard_current_countNum, ARRAY[] AS onboard_currentAiRecommendNum, ARRAY[] AS onboard_currentPrecisionAiRecommendNum, ARRAY[] AS eliminate_current_countNum, ARRAY[] AS eliminate_currentAiRecommendNum, ARRAY[] AS eliminate_currentPrecisionAiRecommendNum, dt FROM mv_application_offer";
    }

    private String buildCompanyOfferAcceptSubQuery() {
        return "SELECT tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated, add_date, event_date, user_roles, ARRAY[] AS submit_to_job_current_countNum, ARRAY[] AS submit_to_job_currentAiRecommendNum, ARRAY[] AS submit_to_job_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_job_currentStayedOver, ARRAY[] AS submit_to_client_current_countNum, ARRAY[] AS submit_to_client_currentAiRecommendNum, ARRAY[] AS submit_to_client_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_client_currentStayedOver, ARRAY[] AS current_interview1, ARRAY[] AS current_interview2, ARRAY[] AS current_two_or_more_interviews, ARRAY[] AS current_interview_final, ARRAY[] AS current_interview_total, ARRAY[] AS unique_interview_talents, ARRAY[] AS current_interview_total_process, ARRAY[] AS currentInterviewTotalAiRecommendNum, ARRAY[] AS currentInterviewTotalProcessAiRecommendNum, ARRAY[] AS currentInterview1AiRecommendNum, ARRAY[] AS currentInterview2AiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsAiRecommendNum, ARRAY[] AS currentInterviewFinalAiRecommendNum, ARRAY[] AS currentInterviewTotalPrecisionAiRecommendNum, ARRAY[] AS currentInterviewNumProcessPrecisionAIRecommend, ARRAY[] AS currentInterview1PrecisionAiRecommendNum, ARRAY[] AS currentInterview2PrecisionAiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum, ARRAY[] AS currentInterviewFinalPrecisionAiRecommendNum, ARRAY[] AS reserve_current_interview_total, ARRAY[] AS reserve_interview_currentAiRecommendNum, ARRAY[] AS reserve_interview_currentPrecisionAiRecommendNum, ARRAY[] AS offer_current_countNum, ARRAY[] AS offer_currentAiRecommendNum, ARRAY[] AS offer_currentPrecisionAiRecommendNum, offer_accept_current_countNum, offer_accept_currentAiRecommendNum, offer_accept_currentPrecisionAiRecommendNum, ARRAY[] AS onboard_current_countNum, ARRAY[] AS onboard_currentAiRecommendNum, ARRAY[] AS onboard_currentPrecisionAiRecommendNum, ARRAY[] AS eliminate_current_countNum, ARRAY[] AS eliminate_currentAiRecommendNum, ARRAY[] AS eliminate_currentPrecisionAiRecommendNum, dt FROM mv_application_offer_accept";
    }

    private String buildCompanyOnboardSubQuery() {
        return "SELECT tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated, add_date, event_date, user_roles, ARRAY[] AS submit_to_job_current_countNum, ARRAY[] AS submit_to_job_currentAiRecommendNum, ARRAY[] AS submit_to_job_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_job_currentStayedOver, ARRAY[] AS submit_to_client_current_countNum, ARRAY[] AS submit_to_client_currentAiRecommendNum, ARRAY[] AS submit_to_client_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_client_currentStayedOver, ARRAY[] AS current_interview1, ARRAY[] AS current_interview2, ARRAY[] AS current_two_or_more_interviews, ARRAY[] AS current_interview_final, ARRAY[] AS current_interview_total, ARRAY[] AS unique_interview_talents, ARRAY[] AS current_interview_total_process, ARRAY[] AS currentInterviewTotalAiRecommendNum, ARRAY[] AS currentInterviewTotalProcessAiRecommendNum, ARRAY[] AS currentInterview1AiRecommendNum, ARRAY[] AS currentInterview2AiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsAiRecommendNum, ARRAY[] AS currentInterviewFinalAiRecommendNum, ARRAY[] AS currentInterviewTotalPrecisionAiRecommendNum, ARRAY[] AS currentInterviewNumProcessPrecisionAIRecommend, ARRAY[] AS currentInterview1PrecisionAiRecommendNum, ARRAY[] AS currentInterview2PrecisionAiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum, ARRAY[] AS currentInterviewFinalPrecisionAiRecommendNum, ARRAY[] AS reserve_current_interview_total, ARRAY[] AS reserve_interview_currentAiRecommendNum, ARRAY[] AS reserve_interview_currentPrecisionAiRecommendNum, ARRAY[] AS offer_current_countNum, ARRAY[] AS offer_currentAiRecommendNum, ARRAY[] AS offer_currentPrecisionAiRecommendNum, ARRAY[] AS offer_accept_current_countNum, ARRAY[] AS offer_accept_currentAiRecommendNum, ARRAY[] AS offer_accept_currentPrecisionAiRecommendNum, onboard_current_countNum, onboard_currentAiRecommendNum, onboard_currentPrecisionAiRecommendNum, ARRAY[] AS eliminate_current_countNum, ARRAY[] AS eliminate_currentAiRecommendNum, ARRAY[] AS eliminate_currentPrecisionAiRecommendNum, dt FROM mv_application_onboard";
    }

    private String buildCompanyEliminateSubQuery() {
        return "SELECT tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated, add_date, event_date, user_roles, ARRAY[] AS submit_to_job_current_countNum, ARRAY[] AS submit_to_job_currentAiRecommendNum, ARRAY[] AS submit_to_job_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_job_currentStayedOver, ARRAY[] AS submit_to_client_current_countNum, ARRAY[] AS submit_to_client_currentAiRecommendNum, ARRAY[] AS submit_to_client_currentPrecisionAiRecommendNum, ARRAY[] AS submit_to_client_currentStayedOver, ARRAY[] AS current_interview1, ARRAY[] AS current_interview2, ARRAY[] AS current_two_or_more_interviews, ARRAY[] AS current_interview_final, ARRAY[] AS current_interview_total, ARRAY[] AS unique_interview_talents, ARRAY[] AS current_interview_total_process, ARRAY[] AS currentInterviewTotalAiRecommendNum, ARRAY[] AS currentInterviewTotalProcessAiRecommendNum, ARRAY[] AS currentInterview1AiRecommendNum, ARRAY[] AS currentInterview2AiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsAiRecommendNum, ARRAY[] AS currentInterviewFinalAiRecommendNum, ARRAY[] AS currentInterviewTotalPrecisionAiRecommendNum, ARRAY[] AS currentInterviewNumProcessPrecisionAIRecommend, ARRAY[] AS currentInterview1PrecisionAiRecommendNum, ARRAY[] AS currentInterview2PrecisionAiRecommendNum, ARRAY[] AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum, ARRAY[] AS currentInterviewFinalPrecisionAiRecommendNum, ARRAY[] AS reserve_current_interview_total, ARRAY[] AS reserve_interview_currentAiRecommendNum, ARRAY[] AS reserve_interview_currentPrecisionAiRecommendNum, ARRAY[] AS offer_current_countNum, ARRAY[] AS offer_currentAiRecommendNum, ARRAY[] AS offer_currentPrecisionAiRecommendNum, ARRAY[] AS offer_accept_current_countNum, ARRAY[] AS offer_accept_currentAiRecommendNum, ARRAY[] AS offer_accept_currentPrecisionAiRecommendNum, ARRAY[] AS onboard_current_countNum, ARRAY[] AS onboard_currentAiRecommendNum, ARRAY[] AS onboard_currentPrecisionAiRecommendNum, eliminate_current_countNum, eliminate_currentAiRecommendNum, eliminate_currentPrecisionAiRecommendNum, dt FROM mv_application_emiminate";
    }
}