package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.optimizer.SqlQueryOptimizer;
import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

/**
 * 团队层级处理器
 * 实现 mv_team_hierarchy 物化视图逻辑，计算团队父子关系
 * 优化基于字符串的层级匹配操作，使用高效的字符串函数
 */
public class TeamHierarchyProcessor extends OptimizedSqlProcessor {

    private static final String TARGET_TABLE = "dwd_apn.dwd_team_hierarchy";
    private static final String PROCESSOR_NAME = "TeamHierarchyProcessor";

    public TeamHierarchyProcessor() {
        super(PROCESSOR_NAME, TARGET_TABLE);
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.DIMENSION;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "ods_apn.permission_team"
        };
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableSql = String.format("""
            CREATE TABLE IF NOT EXISTS %s (
                child_team_id BIGINT,
                child_team_name STRING,
                child_team_code STRING,
                parent_team_id BIGINT,
                parent_team_name STRING,
                parent_team_code STRING,
                parent_team_parent_id BIGINT,
                parent_team_level INT,
                parent_team_is_leaf BOOLEAN,
                tenant_id BIGINT,
                dt STRING,
                PRIMARY KEY (child_team_id, parent_team_id, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )
            """, TARGET_TABLE, getStandardPaimonTableConfig());

        // 直接执行建表 SQL，优化配置已在 WITH 子句中
        tableEnv.executeSql(createTableSql);
        System.out.println("✅ Created target table: " + TARGET_TABLE);
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        // 创建优化的团队层级计算 SQL
        String teamHierarchySql = buildTeamHierarchyQuery();
        
        System.out.println("Executing team hierarchy processing with optimized SQL:");
        System.out.println(teamHierarchySql);
        
        statementSet.addInsertSql(teamHierarchySql);
    }

    /**
     * 构建团队层级计算查询
     * 实现团队层级关系计算逻辑，包括父子关系的 LIKE 匹配优化
     * 
     * @return 优化的团队层级 SQL 查询
     */
    private String buildTeamHierarchyQuery() {
        String hints = getOptimizedSqlHints();
        
        // 使用 CTE 优化查询结构，提高可读性和性能
        String activeTeamsCte = """
            active_teams AS (
                SELECT 
                    id,
                    name,
                    code,
                    parent_id,
                    level,
                    is_leaf,
                    tenant_id,
                    DATE_FORMAT(LOCALTIMESTAMP, 'yyyy-MM-dd') as dt
                FROM ods_apn.permission_team
                WHERE deleted = 0 
                AND team_category_id IN (15, 20)
                AND code IS NOT NULL 
                AND TRIM(code) <> ''
            )
            """;
        
        // 主查询：计算团队层级关系
        String mainQuery = String.format("""
            INSERT INTO %s
            SELECT 
                child.id AS child_team_id,
                child.name AS child_team_name,
                child.code AS child_team_code,
                parent.id AS parent_team_id,
                parent.name AS parent_team_name,
                parent.code AS parent_team_code,
                parent.parent_id AS parent_team_parent_id,
                parent.level AS parent_team_level,
                parent.is_leaf AS parent_team_is_leaf,
                child.tenant_id,
                child.dt
            FROM active_teams child
            INNER JOIN active_teams parent ON (
                -- 优化的字符串匹配：使用 SUBSTRING 和 LENGTH 函数提高性能
                (child.code LIKE CONCAT(parent.code, '%%') AND LENGTH(child.code) >= LENGTH(parent.code))
                OR child.code = parent.code
            )
            WHERE child.tenant_id = parent.tenant_id
            """, TARGET_TABLE);
        
        // 使用 SqlQueryOptimizer 构建完整的 CTE 查询
        java.util.Map<String, String> cteDefinitions = new java.util.HashMap<>();
        cteDefinitions.put("active_teams", activeTeamsCte.substring(activeTeamsCte.indexOf("AS (") + 4, activeTeamsCte.lastIndexOf(")")));
        
        String completeSql = SqlQueryOptimizer.buildOptimizedMultipleCTE(cteDefinitions, mainQuery);
        
        return hints + "\n" + completeSql;
    }

    /**
     * 获取处理器性能信息
     * 
     * @return 性能配置信息
     */
    @Override
    public String getPerformanceInfo() {
        return super.getPerformanceInfo() + 
               ", Optimization: String matching with SUBSTRING/LENGTH, Lookup cache for team data";
    }
}