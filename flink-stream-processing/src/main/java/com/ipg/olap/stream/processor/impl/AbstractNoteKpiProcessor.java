package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.AbstractTableProcessor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AbstractNoteKpiProcessor extends AbstractTableProcessor {
    private static final Logger log = LoggerFactory.getLogger(AbstractNoteKpiProcessor.class);

    protected AbstractNoteKpiProcessor() {
        super("NoteKpiProcessor", "dwd_apn.dwd_note_kpi");
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        Configuration tableConfig = getDefaultPaimonTableConfig();
        tableConfig.setString("merge-engine", "partial-update");
        String sql = """
            CREATE TABLE IF NOT EXISTS dwd_apn.dwd_note_kpi (
                tenant_id BIGINT,
                note_type STRING,
                note_id BIGINT,
                team_id BIGINT,
                team_name STRING,
                team_parent_id BIGINT,
                team_level INT,
                team_is_leaf TINYINT,
                user_id BIGINT,
                user_name STRING,
                user_activated BOOLEAN,
                add_date TIMESTAMP(3),
                callNoteNum MULTISET<BIGINT>,
                personNoteNum MULTISET<BIGINT>,
                otherNoteNum MULTISET<BIGINT>,
                emailNoteNum MULTISET<BIGINT>,
                videoNoteNum MULTISET<BIGINT>,
                iciNum MULTISET<BIGINT>,
                noteCount MULTISET<BIGINT>,
                unique_talent_ids MULTISET<BIGINT>,
                application_note_count_num MULTISET<BIGINT>,
                talent_tracking_note_count_num MULTISET<BIGINT>,
                talent_tracking_note_ids MULTISET<BIGINT>,
                dt STRING,
                PRIMARY KEY (tenant_id, note_type, note_id,  dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )
            """.formatted(getStandardPaimonTableConfig());
        tableEnv.executeSql(sql);
        log.info("Created target table dwd_apn.dwd_note_kpi");
    }

    protected String teamSql() {
        return """
            SELECT
                child.id AS child_team_id,
                child.name AS child_team_name,
                child.code AS child_team_code,
                parent.id AS parent_team_id,
                parent.name AS parent_team_name,
                parent.code AS parent_team_code,
                parent.parent_id AS parent_team_parent_id,
                parent.level AS parent_team_level,
                parent.is_leaf AS parent_team_is_leaf,
                child.tenant_id
            FROM ods_apn.permission_team AS child
            INNER JOIN ods_apn.permission_team AS parent ON (
                child.code LIKE CONCAT(parent.code, '%')
                OR child.code = parent.code
            )
            WHERE child.deleted = 0 AND child.team_category_id IN (15, 20)
            AND parent.deleted = 0 AND parent.team_category_id IN (15, 20)
            """;
    }

}
