package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import com.ipg.olap.stream.optimizer.SqlQueryOptimizer;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 面试阶段处理器
 * 实现 mv_application_interview 的复杂面试阶段逻辑
 * 
 * 功能包括：
 * - 处理不同面试阶段（interview1、interview2、final）的 BITMAP_AGG 逻辑
 * - AI 推荐跟踪功能和面试阶段精准度指标计算
 * - 当前 vs 历史面试指标的状态管理和计算逻辑
 */
public class InterviewProcessor extends OptimizedSqlProcessor {

    private static final Logger log = LoggerFactory.getLogger(InterviewProcessor.class);

    public InterviewProcessor() {
        super("InterviewProcessor", "dwd_application_interview");
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.AGGREGATION;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "dwd_apn.dwd_application_wide",
            "dwd_apn.dwd_application_fact"
        };
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        try {
            log.info("开始处理面试阶段数据 - {}", getPerformanceInfo());

            // 构建并执行面试处理 SQL
            String interviewSql = buildInterviewProcessingSql();
            log.info("执行面试处理 SQL: {}", interviewSql);

            statementSet.addInsertSql(interviewSql);

            log.info("面试阶段数据处理完成");

        } catch (Exception e) {
            log.error("面试阶段数据处理失败", e);
            throw new RuntimeException("InterviewProcessor 处理失败", e);
        }
    }

    /**
     * 创建面试阶段目标表
     */
    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableSql = """
            CREATE TABLE IF NOT EXISTS dwd_apn.dwd_application_interview (
                tenant_id BIGINT,
                company_id BIGINT,
                job_id BIGINT,
                job_pteam_id BIGINT,
                team_id BIGINT,
                team_name STRING,
                user_id BIGINT,
                user_name STRING,
                user_activated BOOLEAN,
                add_date TIMESTAMP(3),
                event_date TIMESTAMP(3),
                user_roles MULTISET<INT>,
                
                -- 历史面试指标
                interview1 MULTISET<BIGINT>,
                interview2 MULTISET<BIGINT>,
                two_or_more_interviews MULTISET<BIGINT>,
                interview_final MULTISET<BIGINT>,
                interview_total MULTISET<BIGINT>,
                unique_interview_talents MULTISET<BIGINT>,
                interview_total_process MULTISET<BIGINT>,
                
                -- AI 推荐历史指标
                interview_total_ai_recommend_num MULTISET<BIGINT>,
                interview_total_process_ai_recommend_num MULTISET<BIGINT>,
                interview1_ai_recommend_num MULTISET<BIGINT>,
                interview2_ai_recommend_num MULTISET<BIGINT>,
                two_or_more_interviews_ai_recommend_num MULTISET<BIGINT>,
                interview_final_ai_recommend_num MULTISET<BIGINT>,
                
                -- AI 推荐精准度历史指标
                interview_total_precision_ai_recommend_num MULTISET<BIGINT>,
                interview_num_process_precision_ai_recommend MULTISET<BIGINT>,
                interview1_precision_ai_recommend_num MULTISET<BIGINT>,
                interview2_precision_ai_recommend_num MULTISET<BIGINT>,
                two_or_more_interviews_precision_ai_recommend_num MULTISET<BIGINT>,
                interview_final_precision_ai_recommend_num MULTISET<BIGINT>,
                
                -- 当前面试指标
                current_interview1 MULTISET<BIGINT>,
                current_interview2 MULTISET<BIGINT>,
                current_two_or_more_interviews MULTISET<BIGINT>,
                current_interview_final MULTISET<BIGINT>,
                current_interview_total MULTISET<BIGINT>,
                current_interview_total_process MULTISET<BIGINT>,
                
                -- 当前 AI 推荐指标
                current_interview_total_ai_recommend_num MULTISET<BIGINT>,
                current_interview_total_process_ai_recommend_num MULTISET<BIGINT>,
                current_interview1_ai_recommend_num MULTISET<BIGINT>,
                current_interview2_ai_recommend_num MULTISET<BIGINT>,
                current_two_or_more_interviews_ai_recommend_num MULTISET<BIGINT>,
                current_interview_final_ai_recommend_num MULTISET<BIGINT>,
                
                -- 当前 AI 推荐精准度指标
                current_interview_total_precision_ai_recommend_num MULTISET<BIGINT>,
                current_interview_num_process_precision_ai_recommend MULTISET<BIGINT>,
                current_interview1_precision_ai_recommend_num MULTISET<BIGINT>,
                current_interview2_precision_ai_recommend_num MULTISET<BIGINT>,
                current_two_or_more_interviews_precision_ai_recommend_num MULTISET<BIGINT>,
                current_interview_final_precision_ai_recommend_num MULTISET<BIGINT>,
                
                dt STRING,
                PRIMARY KEY (tenant_id, company_id, job_id, team_id, user_id, add_date, event_date, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )
            """.formatted(getStandardPaimonConfig());

        tableEnv.executeSql(createTableSql);
        log.info("面试阶段目标表创建完成");
    }

    /**
     * 获取标准的 Paimon 表配置
     */
    private String getStandardPaimonConfig() {
        return String.join(",\n                ",
            "'write-buffer-size' = '512mb'",
            "'sink.parallelism' = '4'", 
            "'lookup.cache.ttl' = '1h'",
            "'compaction.min.file-num' = '5'",
            "'changelog-producer' = 'lookup'",
            "'precommit-compact' = 'true'",
            "'compaction.max.file-num' = '50'",
            "'snapshot.time-retained' = '1h'"
        );
    }

    /**
     * 构建面试处理 SQL
     * 实现复杂的 BITMAP_AGG 逻辑和 AI 推荐跟踪功能
     */
    private String buildInterviewProcessingSql() {
        // 构建 CTE 定义
        Map<String, String> cteDefinitions = new LinkedHashMap<>();

        // CTE 1: 获取最大进度子查询
        cteDefinitions.put("max_progress_subquery", """
            SELECT 
                talent_recruitment_process_id AS application_id,
                MAX(progress) AS progress
            FROM dwd_apn.dwd_application_fact fact
            WHERE node_type = 30
            GROUP BY talent_recruitment_process_id
            """);

        // 主查询：面试阶段聚合逻辑
        String mainQuery = String.format("""
            INSERT INTO dwd_apn.dwd_application_interview
            SELECT 
                tenant_id,
                company_id,
                job_id,
                job_pteam_id,
                team_id,
                team_name,
                user_id,
                user_name,
                user_activated,
                add_date,
                event_date,
                %s,
                
                -- 历史面试指标
                %s,
                %s,
                %s,
                %s,
                %s,
                %s,
                %s,
                
                -- AI 推荐历史指标
                %s,
                %s,
                %s,
                %s,
                %s,
                %s,
                
                -- AI 推荐精准度历史指标
                %s,
                %s,
                %s,
                %s,
                %s,
                %s,
                
                -- 当前面试指标
                %s,
                %s,
                %s,
                %s,
                %s,
                %s,
                
                -- 当前 AI 推荐指标
                %s,
                %s,
                %s,
                %s,
                %s,
                %s,
                
                -- 当前 AI 推荐精准度指标
                %s,
                %s,
                %s,
                %s,
                %s,
                %s,
                
                dt
            FROM dwd_apn.dwd_application_wide AS application
            LEFT JOIN max_progress_subquery 
                ON application_id = talent_recruitment_process_id
            WHERE node_type = 30
            GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, 
                     user_id, user_name, user_activated, add_date, event_date, dt
            """,
            // 用户角色聚合
            SqlQueryOptimizer.buildBitmapAggEquivalent("user_role", "TRUE", "user_roles"),
            
            // 历史面试指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "application.progress = 1 AND node_type = 30", "interview1"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "application.progress = 2 AND node_type = 30", "interview2"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "application.progress >= 2 AND node_type = 30 AND final_round <> 1", "two_or_more_interviews"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "final_round = 1 AND node_type = 30", "interview_final"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", "TRUE", "interview_total"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_id", "TRUE", "unique_interview_talents"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", "TRUE", "interview_total_process"),
            
            // AI 推荐历史指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "ai_score IS NOT NULL AND node_type = 30", "interview_total_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "ai_score IS NOT NULL", "interview_total_process_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "application.progress = 1 AND ai_score IS NOT NULL", "interview1_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "application.progress = 2 AND ai_score IS NOT NULL", "interview2_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "application.progress >= 2 AND final_round <> 1 AND node_type = 30 AND ai_score IS NOT NULL", 
                "two_or_more_interviews_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "final_round = 1 AND ai_score IS NOT NULL", "interview_final_ai_recommend_num"),
            
            // AI 推荐精准度历史指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "recommend_feedback_id IS NOT NULL AND node_type = 30", "interview_total_precision_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "recommend_feedback_id IS NOT NULL", "interview_num_process_precision_ai_recommend"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "application.progress = 1 AND recommend_feedback_id IS NOT NULL", "interview1_precision_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "application.progress = 2 AND recommend_feedback_id IS NOT NULL", "interview2_precision_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "application.progress >= 2 AND final_round <> 1 AND node_type = 30 AND recommend_feedback_id IS NOT NULL", 
                "two_or_more_interviews_precision_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "final_round = 1 AND recommend_feedback_id IS NOT NULL", "interview_final_precision_ai_recommend_num"),
            
            // 当前面试指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "application.progress = 1 AND node_status = 1 AND node_type = 30 AND max_progress_subquery.progress = 1", 
                "current_interview1"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "application.progress = 2 AND node_status = 1 AND node_type = 30 AND max_progress_subquery.progress = 2", 
                "current_interview2"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "application.progress >= 2 AND final_round <> 1 AND node_status = 1 AND node_type = 30", 
                "current_two_or_more_interviews"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "final_round = 1 AND node_status = 1 AND node_type = 30", "current_interview_final"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "node_status = 1 AND node_type = 30", "current_interview_total"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "node_status = 1", "current_interview_total_process"),
            
            // 当前 AI 推荐指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "ai_score IS NOT NULL AND node_status = 1 AND node_type = 30", "current_interview_total_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "ai_score IS NOT NULL AND node_status = 1", "current_interview_total_process_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "application.progress = 1 AND node_status = 1 AND max_progress_subquery.progress = 1 AND ai_score IS NOT NULL", 
                "current_interview1_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "application.progress = 2 AND node_status = 1 AND max_progress_subquery.progress = 2 AND ai_score IS NOT NULL", 
                "current_interview2_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "application.progress >= 2 AND final_round <> 1 AND node_status = 1 AND node_type = 30 AND ai_score IS NOT NULL", 
                "current_two_or_more_interviews_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "final_round = 1 AND node_status = 1 AND ai_score IS NOT NULL", "current_interview_final_ai_recommend_num"),
            
            // 当前 AI 推荐精准度指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "recommend_feedback_id IS NOT NULL AND node_status = 1 AND node_type = 30", 
                "current_interview_total_precision_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "recommend_feedback_id IS NOT NULL AND node_status = 1", 
                "current_interview_num_process_precision_ai_recommend"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "application.progress = 1 AND max_progress_subquery.progress = 1 AND node_status = 1 AND recommend_feedback_id IS NOT NULL", 
                "current_interview1_precision_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "application.progress = 2 AND max_progress_subquery.progress = 2 AND node_status = 1 AND recommend_feedback_id IS NOT NULL", 
                "current_interview2_precision_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("node_id", 
                "application.progress >= 2 AND final_round <> 1 AND node_status = 1 AND node_type = 30 AND recommend_feedback_id IS NOT NULL", 
                "current_two_or_more_interviews_precision_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("talent_recruitment_process_id", 
                "application.final_round = 1 AND node_status = 1 AND recommend_feedback_id IS NOT NULL", 
                "current_interview_final_precision_ai_recommend_num")
        );

        // 使用 CTE 构建完整 SQL
        return SqlQueryOptimizer.buildOptimizedMultipleCTE(cteDefinitions, mainQuery);
    }

    /**
     * 获取处理器描述信息
     */
    @Override
    public String toString() {
        return String.format("InterviewProcessor{processorType=%s, targetTable=%s}", 
                           getProcessorType(), getTargetTableName());
    }
}