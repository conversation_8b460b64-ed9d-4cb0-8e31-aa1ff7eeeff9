package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import com.ipg.olap.stream.optimizer.SqlQueryOptimizer;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 入职阶段处理器
 * 实现 mv_application_onboard 入职阶段逻辑
 * 
 * 功能包括：
 * - 入职阶段的基础指标统计
 * - AI 推荐和精准度跟踪
 * - 当前状态和历史状态的区分处理
 */
public class OnboardProcessor extends OptimizedSqlProcessor {

    private static final Logger log = LoggerFactory.getLogger(OnboardProcessor.class);

    public OnboardProcessor() {
        super("OnboardProcessor", "dwd_application_onboard");
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.AGGREGATION;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "dwd_apn.dwd_application_wide"
        };
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        try {
            log.info("开始处理入职阶段数据 - {}", getPerformanceInfo());

            // 构建并执行入职处理 SQL
            String onboardSql = buildOnboardProcessingSql();
            log.info("执行入职处理 SQL: {}", onboardSql);

            statementSet.addInsertSql(onboardSql);

            log.info("入职阶段数据处理完成");

        } catch (Exception e) {
            log.error("入职阶段数据处理失败", e);
            throw new RuntimeException("OnboardProcessor 处理失败", e);
        }
    }

    /**
     * 创建入职阶段目标表
     */
    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableSql = """
            CREATE TABLE IF NOT EXISTS `dwd_apn`.`dwd_application_onboard` (
                tenant_id BIGINT,
                company_id BIGINT,
                job_id BIGINT,
                job_pteam_id BIGINT,
                team_id BIGINT,
                team_name STRING,
                user_id BIGINT,
                user_name STRING,
                user_activated BOOLEAN,
                add_date TIMESTAMP(3),
                event_date TIMESTAMP(3),
                user_roles MULTISET<INT>,
                
                -- 入职基础指标
                onboard_count_num MULTISET<BIGINT>,
                onboard_ai_recommend_count_num MULTISET<BIGINT>,
                onboard_precision_ai_recommend_num MULTISET<BIGINT>,
                
                -- 当前入职指标
                onboard_current_count_num MULTISET<BIGINT>,
                onboard_current_ai_recommend_num MULTISET<BIGINT>,
                onboard_current_precision_ai_recommend_num MULTISET<BIGINT>,
                
                dt STRING,
                PRIMARY KEY (tenant_id, company_id, job_id, team_id, user_id, add_date, event_date, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )
            """.formatted(getStandardPaimonConfig());

        tableEnv.executeSql(createTableSql);
        log.info("入职阶段目标表创建完成");
    }

    /**
     * 获取标准的 Paimon 表配置
     */
    private String getStandardPaimonConfig() {
        return String.join(",\n                ",
            "'write-buffer-size' = '512mb'",
            "'sink.parallelism' = '4'", 
            "'lookup.cache.ttl' = '1h'",
            "'compaction.min.file-num' = '5'",
            "'changelog-producer' = 'lookup'",
            "'precommit-compact' = 'true'",
            "'compaction.max.file-num' = '50'",
            "'snapshot.time-retained' = '1h'"
        );
    }

    /**
     * 构建入职处理 SQL
     */
    private String buildOnboardProcessingSql() {
        return String.format("""
            INSERT INTO dwd_apn.dwd_application_onboard
            SELECT 
                tenant_id,
                company_id,
                job_id,
                job_pteam_id,
                team_id,
                team_name,
                user_id,
                user_name,
                user_activated,
                add_date,
                event_date,
                %s,
                
                -- 入职基础指标
                %s,
                %s,
                %s,
                
                -- 当前入职指标
                %s,
                %s,
                %s,
                
                dt
            FROM dwd_apn.dwd_application_wide AS application
            WHERE node_type = 60
            GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, 
                     user_id, user_name, user_activated, add_date, event_date, dt
            """,
            // 用户角色聚合
            SqlQueryOptimizer.buildBitmapAggEquivalent("user_role", "TRUE", "user_roles"),
            
            // 入职基础指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.node_id", "TRUE", "onboard_count_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "ai_score IS NOT NULL", "onboard_ai_recommend_count_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "recommend_feedback_id IS NOT NULL", "onboard_precision_ai_recommend_num"),
            
            // 当前入职指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.node_id", 
                "application.node_status = 1", "onboard_current_count_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "ai_score IS NOT NULL AND application.node_status = 1", "onboard_current_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "recommend_feedback_id IS NOT NULL AND application.node_status = 1", 
                "onboard_current_precision_ai_recommend_num")
        );
    }

    /**
     * 获取处理器描述信息
     */
    @Override
    public String toString() {
        return String.format("OnboardProcessor{processorType=%s, targetTable=%s}", 
                           getProcessorType(), getTargetTableName());
    }
}