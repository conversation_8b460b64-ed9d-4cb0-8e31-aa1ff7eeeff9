package com.ipg.olap.stream.job;

import com.ipg.olap.stream.config.EnvConfiguration;
import com.ipg.olap.stream.config.PaimonConfig;
import com.ipg.olap.stream.factory.TableProcessorFactory;
import com.ipg.olap.stream.monitor.PerformanceMonitor;
import com.ipg.olap.stream.optimizer.AdvancedSqlOptimizer;
import com.ipg.olap.stream.processor.AbstractTableProcessor;
import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 分层的 Paimon DWD 作业
 * 
 * 将原来的单一重量级作业拆分为多个轻量级作业：
 * 1. 基础层作业：处理 Fact 和 Dimension 表
 * 2. 宽表层作业：处理 Wide 表
 * 3. 聚合层作业：处理 ToJob、ToClient 等聚合表
 * 
 * 每个作业独立运行，减少资源竞争和算子复杂度
 */
public class PaimonDwdLayeredJob {

    private static final Logger log = LoggerFactory.getLogger(PaimonDwdLayeredJob.class);

    public static void main(String[] args) {
        if (args.length < 2) {
            log.error("Usage: PaimonDwdLayeredJob <config-file> <layer>");
            log.error("Layers: foundation, wide, aggregation");
            System.exit(1);
        }

        String configFile = args[0];
        String layer = args[1];

        try {
            switch (layer.toLowerCase()) {
                case "foundation":
                    runFoundationLayer(configFile);
                    break;
                case "wide":
                    runWideLayer(configFile);
                    break;
                case "aggregation":
                    runAggregationLayer(configFile);
                    break;
                default:
                    log.error("Unknown layer: {}. Supported layers: foundation, wide, aggregation", layer);
                    System.exit(1);
            }
        } catch (Exception e) {
            log.error("Failed to run layer: " + layer, e);
            System.exit(1);
        }
    }

    /**
     * 运行基础层作业：处理 Fact 和 Dimension 表
     * 这些表是其他表的基础，需要优先处理
     */
    private static void runFoundationLayer(String configFile) throws Exception {
        String layerName = "foundation";
        PerformanceMonitor.monitorJobStartup("FoundationLayer", layerName);

        log.info("🚀 Starting Foundation Layer Job (Fact + Dimension)");

        StreamExecutionEnvironment env = createOptimizedEnvironment(configFile, layerName);
        StreamTableEnvironment tableEnv = createOptimizedTableEnvironment(env);

        // 设置基础层的优化配置
        optimizeForFoundationLayer(tableEnv);
        PerformanceMonitor.monitorTableEnvironmentConfig(tableEnv, layerName);

        // 只处理基础表
        List<String> foundationProcessors = Arrays.asList(
            "OptimizedApplicationFactProcessor",
            "ApplicationDimensionProcessor"
        );

        StatementSet statementSet = tableEnv.createStatementSet();
        processSelectedProcessors(tableEnv, statementSet, foundationProcessors);

        log.info("📊 Foundation Layer Job submitted successfully");
        statementSet.execute();

        PerformanceMonitor.monitorJobCompletion("FoundationLayer", layerName);
    }

    /**
     * 运行宽表层作业：处理 Wide 表
     * 依赖基础层的 Fact 和 Dimension 表
     */
    private static void runWideLayer(String configFile) throws Exception {
        log.info("🚀 Starting Wide Layer Job");
        
        StreamExecutionEnvironment env = createOptimizedEnvironment(configFile, "wide");
        StreamTableEnvironment tableEnv = createOptimizedTableEnvironment(env);
        
        // 设置宽表层的优化配置
        optimizeForWideLayer(tableEnv);
        
        // 只处理宽表
        List<String> wideProcessors = Arrays.asList(
            "ApplicationWideProcessor"
        );
        
        StatementSet statementSet = tableEnv.createStatementSet();
        processSelectedProcessors(tableEnv, statementSet, wideProcessors);
        
        log.info("📊 Wide Layer Job submitted successfully");
        statementSet.execute();
    }

    /**
     * 运行聚合层作业：处理聚合表
     * 依赖宽表层的数据
     */
    private static void runAggregationLayer(String configFile) throws Exception {
        log.info("🚀 Starting Aggregation Layer Job");
        
        StreamExecutionEnvironment env = createOptimizedEnvironment(configFile, "aggregation");
        StreamTableEnvironment tableEnv = createOptimizedTableEnvironment(env);
        
        // 设置聚合层的优化配置
        optimizeForAggregationLayer(tableEnv);
        
        // 处理聚合表
        List<String> aggregationProcessors = Arrays.asList(
            "ToJobProcessor",
            "ToClientProcessor"
        );
        
        StatementSet statementSet = tableEnv.createStatementSet();
        processSelectedProcessors(tableEnv, statementSet, aggregationProcessors);
        
        log.info("📊 Aggregation Layer Job submitted successfully");
        statementSet.execute();
    }

    /**
     * 创建优化的执行环境
     */
    private static StreamExecutionEnvironment createOptimizedEnvironment(String configFile, String layer) {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        EnvConfiguration.initConfig(configFile);
        Map<String, String> actionConfig = EnvConfiguration.getCdcActionConfig();
        
        // 根据层级设置不同的并行度
        int parallelism = getOptimalParallelism(layer);
        env.setParallelism(parallelism);
        
        // 设置检查点间隔
        int checkpointInterval = Integer.parseInt(actionConfig.getOrDefault("checkpoint.interval", "60000"));
        env.enableCheckpointing(checkpointInterval);
        
        log.info("✅ Environment created for layer: {}, parallelism: {}", layer, parallelism);
        return env;
    }

    /**
     * 创建优化的表环境
     */
    private static StreamTableEnvironment createOptimizedTableEnvironment(StreamExecutionEnvironment env) {
        EnvironmentSettings settings = EnvironmentSettings
            .newInstance()
            .inStreamingMode()
            .build();
        
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env, settings);
        
        // 设置 Paimon 环境
        PaimonConfig.setupPaimonEnvironment(tableEnv);
        
        return tableEnv;
    }

    /**
     * 根据层级获取最优并行度
     */
    private static int getOptimalParallelism(String layer) {
        return switch (layer.toLowerCase()) {
            case "foundation" -> 4;  // 基础层：中等并行度，平衡吞吐量和资源使用
            case "wide" -> 6;        // 宽表层：较高并行度，处理复杂关联
            case "aggregation" -> 8; // 聚合层：最高并行度，处理大量聚合计算
            default -> 4;
        };
    }

    /**
     * 为基础层优化配置
     */
    private static void optimizeForFoundationLayer(StreamTableEnvironment tableEnv) {
        Configuration config = tableEnv.getConfig().getConfiguration();
        
        // 基础层优化：注重数据一致性和稳定性
        config.setString("table.exec.mini-batch.enabled", "true");
        config.setString("table.exec.mini-batch.allow-latency", "2s");
        config.setString("table.exec.mini-batch.size", "2000");
        config.setString("table.exec.state.ttl", "2h");
        
        log.info("✅ Foundation layer optimizations applied");
    }

    /**
     * 为宽表层优化配置
     */
    private static void optimizeForWideLayer(StreamTableEnvironment tableEnv) {
        Configuration config = tableEnv.getConfig().getConfiguration();
        
        // 宽表层优化：注重关联性能
        config.setString("table.exec.mini-batch.enabled", "true");
        config.setString("table.exec.mini-batch.allow-latency", "3s");
        config.setString("table.exec.mini-batch.size", "3000");
        config.setString("table.optimizer.join-reorder-enabled", "true");
        config.setString("table.optimizer.join.broadcast-threshold", "20MB");
        config.setString("table.exec.state.ttl", "1h");
        
        log.info("✅ Wide layer optimizations applied");
    }

    /**
     * 为聚合层优化配置
     */
    private static void optimizeForAggregationLayer(StreamTableEnvironment tableEnv) {
        Configuration config = tableEnv.getConfig().getConfiguration();
        
        // 聚合层优化：注重聚合性能和吞吐量
        config.setString("table.exec.mini-batch.enabled", "true");
        config.setString("table.exec.mini-batch.allow-latency", "5s");
        config.setString("table.exec.mini-batch.size", "5000");
        config.setString("table.exec.state.ttl", "4h");
        
        log.info("✅ Aggregation layer optimizations applied");
    }

    /**
     * 处理选定的处理器
     */
    private static void processSelectedProcessors(StreamTableEnvironment tableEnv,
                                                StatementSet statementSet,
                                                List<String> processorNames) {
        for (String processorName : processorNames) {
            if (TableProcessorFactory.hasProcessor(processorName)) {
                AbstractTableProcessor processor = (AbstractTableProcessor) TableProcessorFactory.getProcessor(processorName);

                // 开始性能监控
                PerformanceMonitor.startProcessorMonitoring(processorName);

                log.info("🔄 Processing: {}", processorName);
                processor.process(tableEnv, statementSet);

                // 结束性能监控
                PerformanceMonitor.endProcessorMonitoring(processorName);

                // 输出性能信息
                if (processor instanceof OptimizedSqlProcessor optimizedProcessor) {
                    log.info("📈 Performance info: {}", optimizedProcessor.getPerformanceInfo());
                }
            } else {
                log.warn("⚠️ Processor not found: {}", processorName);
            }
        }
    }
}
