package com.ipg.olap.stream.job;

import com.ipg.olap.stream.config.EnvConfiguration;
import com.ipg.olap.stream.config.PaimonConfig;
import com.ipg.olap.stream.factory.TableProcessorFactory;
import com.ipg.olap.stream.processor.AbstractTableProcessor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.RestOptions;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.Map;

public class PaimonDwdJob {

    public static void main(String[] args) {
        // 本地开发时打开这些
//        Configuration flinkConfig = new Configuration();
//        // 关闭 upsert-materialize
//        flinkConfig.setString("table.exec.sink.upsert-materialize", "NONE");
//        flinkConfig.setString("state.backend.type", "rocksdb");
//        flinkConfig.setString("execution.checkpointing.dir", "file:///Users/<USER>/ipg/backend/apn-flink-ETL/flink/data/checkpoints");
//        flinkConfig.setString("execution.checkpointing.timeout", "1200s");
//        flinkConfig.setString("taskmanager.memory.managed.size", "512m");
//
//        flinkConfig.setInteger(RestOptions.PORT, 8082);
//        StreamExecutionEnvironment env = StreamExecutionEnvironment.createLocalEnvironmentWithWebUI(flinkConfig);

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        EnvConfiguration.initConfig(args[0]);
        Map<String, String> actionConfig = EnvConfiguration.getCdcActionConfig();

        // 设置并行度和检查点
        env.setParallelism(Integer.parseInt(actionConfig.getOrDefault("parallelism", "1")));
        env.enableCheckpointing(Integer.parseInt(actionConfig.getOrDefault("checkpoint.interval", "60000")));

        // 2. 创建 Table Environment
        EnvironmentSettings settings = EnvironmentSettings
            .newInstance()
            .inStreamingMode()
            .build();
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env, settings);

        // 3. 设置 Paimon 环境
        PaimonConfig.setupPaimonEnvironment(tableEnv);

        // 4. 显示所有注册的处理器
        TableProcessorFactory.printAllProcessors();

        StatementSet statementSet = tableEnv.createStatementSet();
        TableProcessorFactory.getAllProcessors().forEach(processor -> {
            if (processor instanceof AbstractTableProcessor abstractProcessor) {
                abstractProcessor.process(tableEnv, statementSet);
            }
        });
        statementSet.execute();
    }

}
