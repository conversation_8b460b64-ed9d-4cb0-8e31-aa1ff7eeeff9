package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import com.ipg.olap.stream.optimizer.SqlQueryOptimizer;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 应用宽表处理器
 * 实现 mv_application_wide 的复杂关联逻辑，包括 CTE 和用户团队信息连接
 */
public class ApplicationWideProcessor extends OptimizedSqlProcessor {

    private static final Logger log = LoggerFactory.getLogger(ApplicationWideProcessor.class);

    public ApplicationWideProcessor() {
        super("ApplicationWideProcessor", "dwd_apn.dwd_application_wide");
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.WIDE;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "dwd_apn.dwd_application_fact",
            "dwd_apn.dwd_application_dimension", 
            "ods_apn.talent_recruitment_process_kpi_user",
            "ods_apn.user",
            "ods_apn.permission_user_team",
            "ods_apn.permission_team"
        };
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableDDL = """
            CREATE TABLE IF NOT EXISTS %s (
                tenant_id BIGINT,
                company_id BIGINT,
                job_id BIGINT,
                job_pteam_id BIGINT,
                talent_recruitment_process_id BIGINT,
                talent_id BIGINT,
                ai_score DOUBLE,
                recommend_feedback_id BIGINT,
                team_id BIGINT,
                team_name STRING,
                user_id BIGINT,
                user_name STRING,
                user_activated BOOLEAN,
                user_role INT,
                node_id BIGINT,
                node_type INT,
                node_status INT,
                progress INT,
                final_round BOOLEAN,
                add_date TIMESTAMP(3),
                event_date TIMESTAMP(3),
                last_modified_date TIMESTAMP(3),
                note_last_modify_date TIMESTAMP(3),
                dt STRING,
                PRIMARY KEY (talent_recruitment_process_id, user_id, node_id, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )""".formatted(targetTableName, getOptimizedPaimonTableConfig());

        // 直接执行建表 SQL，优化配置已在 WITH 子句中
        tableEnv.executeSql(createTableDDL);
        
        log.info("Optimized wide table created: {}", getTargetTableName());
        log.info("Performance info: {}", getPerformanceInfo());
    }

    /**
     * 获取优化的 Paimon 表配置字符串
     * 基于宽表的特性进行优化配置
     */
    private String getOptimizedPaimonTableConfig() {
        return String.join(",\n                ",
            "'write-buffer-size' = '256mb'",
            "'sink.parallelism' = '4'",
            "'lookup.cache.ttl' = '30m'",
            "'lookup.cache.max-rows' = '100000'",
            "'compaction.min.file-num' = '10'",
            "'changelog-producer' = 'none'",
//            "'precommit-compact' = 'true'",
            "'compaction.max.file-num' = '50'",
            "'snapshot.time-retained' = '1h'",
            "'bucket' = '4'",
            "'file.target-file-size' = '128MB'"
        );
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        // 构建复杂的 CTE 逻辑和宽表关联查询
        String wideTableSQL = buildApplicationWideQuery();
        
        log.info("Executing application wide table processing SQL:");
        log.info(wideTableSQL);

        statementSet.addInsertSql(wideTableSQL);
    }

    /**
     * 构建应用宽表查询 SQL
     * 实现复杂的 CTE 逻辑包括 fact_with_user 子查询处理 KPI 用户关系
     */
    private String buildApplicationWideQuery() {
        // 构建 CTE 定义
        Map<String, String> cteDefinitions = new LinkedHashMap<>();
        cteDefinitions.put("fact_with_user", buildFactWithUserCTE());
        
        // 构建主查询
        String mainQuery = buildMainWideQuery();
        
        // 使用 SqlQueryOptimizer 构建完整的 CTE 查询
        String completeQuery = SqlQueryOptimizer.buildOptimizedMultipleCTE(cteDefinitions, mainQuery);
        
        // 添加性能提示
        String hints = getOptimizedSqlHints();
        
        return String.format("INSERT INTO %s\n%s\n%s", targetTableName, hints, completeQuery);
    }

    /**
     * 构建 fact_with_user CTE
     * 处理 KPI 用户关系和操作员补充逻辑
     */
    private String buildFactWithUserCTE() {
        return """
            -- 从 KPI User 关联
            SELECT 
                fact.talent_recruitment_process_id,
                fact.node_type,
                fact.node_status,
                fact.node_id,
                fact.progress,
                fact.final_round,
                fact.add_date,
                fact.event_date,
                fact.last_modified_date,
                fact.note_last_modify_date,
                kpi.user_id,
                CASE WHEN kpi.user_role IS NOT NULL THEN kpi.user_role ELSE 10000 END AS user_role,
                DATE_FORMAT(fact.add_date, 'yyyy-MM') AS dt
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM `dwd_apn`.`dwd_application_fact`
            ) AS fact
            INNER JOIN `ods_apn`.`talent_recruitment_process_kpi_user` FOR SYSTEM_TIME AS OF fact.proc_time AS kpi
                ON fact.talent_recruitment_process_id = kpi.talent_recruitment_process_id

            UNION ALL

            -- 从 Operator 补充
            SELECT 
                fact.talent_recruitment_process_id,
                fact.node_type,
                fact.node_status,
                fact.node_id,
                fact.progress,
                fact.final_round,
                fact.add_date,
                fact.event_date,
                fact.last_modified_date,
                fact.note_last_modify_date,
                fact.operator AS user_id,
                10 AS user_role,
                DATE_FORMAT(fact.add_date, 'yyyy-MM') AS dt
            FROM `dwd_apn`.`dwd_application_fact` AS fact
            """;
    }

    /**
     * 构建主宽表查询
     * 优化事实表和维度表的关联以及用户团队信息的连接
     */
    private String buildMainWideQuery() {
        return """
            SELECT 
                dim.tenant_id,
                dim.company_id,
                dim.job_id,
                dim.job_pteam_id,
                dim.talent_recruitment_process_id,
                dim.talent_id,
                dim.ai_score,
                dim.recommend_feedback_id,
                pt.id AS team_id,
                pt.name AS team_name,
                fact_with_user.user_id,
                CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) AS user_name,
                u.activated AS user_activated,
                fact_with_user.user_role,
                fact_with_user.node_id,
                fact_with_user.node_type,
                fact_with_user.node_status,
                fact_with_user.progress,
                fact_with_user.final_round,
                fact_with_user.add_date,
                fact_with_user.event_date,
                fact_with_user.last_modified_date,
                fact_with_user.note_last_modify_date,
                fact_with_user.dt
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM fact_with_user
            ) AS fact_with_user
            LEFT JOIN `dwd_apn`.`dwd_application_dimension` FOR SYSTEM_TIME AS OF fact_with_user.proc_time AS dim
                ON fact_with_user.talent_recruitment_process_id = dim.talent_recruitment_process_id
                AND fact_with_user.dt = dim.dt
            LEFT JOIN `ods_apn`.`user` FOR SYSTEM_TIME AS OF fact_with_user.proc_time AS u
                ON fact_with_user.user_id = u.id 
                AND dim.tenant_id = u.tenant_id
            LEFT JOIN `ods_apn`.`permission_user_team` FOR SYSTEM_TIME AS OF fact_with_user.proc_time AS put
                ON u.id = put.user_id 
                AND put.is_primary = 1
            LEFT JOIN `ods_apn`.`permission_team` FOR SYSTEM_TIME AS OF fact_with_user.proc_time AS pt
                ON put.team_id = pt.id
            """;
    }

}