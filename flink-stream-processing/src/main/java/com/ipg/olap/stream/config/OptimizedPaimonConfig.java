package com.ipg.olap.stream.config;

import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 优化的 Paimon 配置管理类
 * 
 * 提供针对不同表类型和工作负载的优化配置：
 * 1. 事实表配置：高吞吐量写入优化
 * 2. 维度表配置：高频查询优化
 * 3. 宽表配置：复杂关联优化
 * 4. 聚合表配置：大数据量聚合优化
 */
public class OptimizedPaimonConfig {
    
    private static final Logger log = LoggerFactory.getLogger(OptimizedPaimonConfig.class);

    /**
     * 表类型枚举
     */
    public enum TableType {
        FACT("事实表"),
        DIMENSION("维度表"),
        WIDE("宽表"),
        AGGREGATION("聚合表"),
        KPI("KPI表");

        private final String description;

        TableType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 获取针对特定表类型优化的 Paimon 配置
     * 
     * @param tableType 表类型
     * @param tableName 表名（用于日志）
     * @return 优化的配置映射
     */
    public static Map<String, String> getOptimizedTableConfig(TableType tableType, String tableName) {
        Map<String, String> config = new HashMap<>();
        
        // 通用基础配置
        addBaseConfig(config);
        
        // 根据表类型添加特定优化
        switch (tableType) {
            case FACT:
                addFactTableOptimizations(config);
                break;
            case DIMENSION:
                addDimensionTableOptimizations(config);
                break;
            case WIDE:
                addWideTableOptimizations(config);
                break;
            case AGGREGATION:
                addAggregationTableOptimizations(config);
                break;
            case KPI:
                addKpiTableOptimizations(config);
                break;
        }
        
        log.info("✅ Optimized Paimon config generated for {} table: {}", tableType.getDescription(), tableName);
        return config;
    }

    /**
     * 添加基础配置
     */
    private static void addBaseConfig(Map<String, String> config) {
        // 基础存储配置
        config.put("changelog-producer", "lookup");
        config.put("precommit-compact", "true");
        config.put("snapshot.time-retained", "1h");
        config.put("snapshot.num-retained.min", "10");
        config.put("snapshot.num-retained.max", "100");
        
        // 压缩配置
        config.put("file.compression", "lz4");
        config.put("compaction.early-max.file-num", "50");
        
        // 性能配置
        config.put("scan.parallelism", "4");
    }

    /**
     * 事实表优化配置
     * 特点：高频写入，中等查询频率，数据量大
     */
    private static void addFactTableOptimizations(Map<String, String> config) {
        // 写入优化
        config.put("write-buffer-size", "512mb");
        config.put("sink.parallelism", "4");
        config.put("bucket", "8");
        config.put("file.target-file-size", "128MB");
        
        // 压缩优化
        config.put("compaction.min.file-num", "5");
        config.put("compaction.max.file-num", "50");
        
        // 查询优化
        config.put("lookup.cache.ttl", "1h");
        config.put("lookup.cache.max-rows", "50000");
        
        log.debug("📊 Applied fact table optimizations: high-throughput writes, moderate query cache");
    }

    /**
     * 维度表优化配置
     * 特点：低频写入，高频查询，数据量小
     */
    private static void addDimensionTableOptimizations(Map<String, String> config) {
        // 写入优化（较小缓冲区，因为写入频率低）
        config.put("write-buffer-size", "128mb");
        config.put("sink.parallelism", "2");
        config.put("bucket", "4");
        config.put("file.target-file-size", "64MB");
        
        // 压缩优化（更积极的压缩）
        config.put("compaction.min.file-num", "2");
        config.put("compaction.max.file-num", "20");
        
        // 查询优化（长缓存时间，大缓存容量）
        config.put("lookup.cache.ttl", "24h");
        config.put("lookup.cache.max-rows", "100000");
        
        log.debug("🔍 Applied dimension table optimizations: long cache TTL, aggressive compaction");
    }

    /**
     * 宽表优化配置
     * 特点：复杂关联，中等写入频率，高查询频率
     */
    private static void addWideTableOptimizations(Map<String, String> config) {
        // 写入优化（平衡写入和查询性能）
        config.put("write-buffer-size", "512mb");
        config.put("sink.parallelism", "6");
        config.put("bucket", "6");
        config.put("file.target-file-size", "128MB");
        
        // 压缩优化
        config.put("compaction.min.file-num", "10");
        config.put("compaction.max.file-num", "50");
        
        // 查询优化（中等缓存时间）
        config.put("lookup.cache.ttl", "30m");
        config.put("lookup.cache.max-rows", "100000");
        
        // 特殊优化：支持复杂关联
        config.put("scan.push-down.enabled", "true");
        config.put("scan.projection.enabled", "true");
        
        log.debug("🔗 Applied wide table optimizations: balanced I/O, projection pushdown");
    }

    /**
     * 聚合表优化配置
     * 特点：大数据量聚合，高吞吐量要求
     */
    private static void addAggregationTableOptimizations(Map<String, String> config) {
        // 写入优化（大缓冲区，高并行度）
        config.put("write-buffer-size", "1gb");
        config.put("sink.parallelism", "8");
        config.put("bucket", "12");
        config.put("file.target-file-size", "256MB");
        
        // 压缩优化（延迟压缩，提高写入性能）
        config.put("compaction.min.file-num", "15");
        config.put("compaction.max.file-num", "100");
        
        // 查询优化（较短缓存时间，因为数据变化频繁）
        config.put("lookup.cache.ttl", "10m");
        config.put("lookup.cache.max-rows", "200000");
        
        // 特殊优化：聚合性能
        config.put("sink.buffer-flush.max-rows", "10000");
        config.put("sink.buffer-flush.interval", "10s");
        
        log.debug("📈 Applied aggregation table optimizations: high throughput, large buffers");
    }

    /**
     * KPI 表优化配置
     * 特点：频繁更新，实时查询需求
     */
    private static void addKpiTableOptimizations(Map<String, String> config) {
        // 写入优化（中等缓冲区，快速刷新）
        config.put("write-buffer-size", "256mb");
        config.put("sink.parallelism", "4");
        config.put("bucket", "8");
        config.put("file.target-file-size", "64MB");
        
        // 压缩优化（频繁压缩，保持查询性能）
        config.put("compaction.min.file-num", "3");
        config.put("compaction.max.file-num", "30");
        
        // 查询优化（短缓存时间，保证数据新鲜度）
        config.put("lookup.cache.ttl", "5m");
        config.put("lookup.cache.max-rows", "50000");
        
        // 特殊优化：实时性
        config.put("sink.buffer-flush.max-rows", "1000");
        config.put("sink.buffer-flush.interval", "1s");
        
        log.debug("⚡ Applied KPI table optimizations: low latency, frequent updates");
    }

    /**
     * 格式化配置为 Paimon WITH 子句
     * 
     * @param config 配置映射
     * @return 格式化的 WITH 子句内容
     */
    public static String formatAsWithClause(Map<String, String> config) {
        return config.entrySet().stream()
            .map(entry -> String.format("'%s' = '%s'", entry.getKey(), entry.getValue()))
            .reduce((a, b) -> a + ",\n                " + b)
            .orElse("");
    }

    /**
     * 获取针对特定数据量的优化配置
     * 
     * @param tableType 表类型
     * @param estimatedRowsPerDay 预估每日行数
     * @return 优化的配置
     */
    public static Map<String, String> getDataVolumeOptimizedConfig(TableType tableType, long estimatedRowsPerDay) {
        Map<String, String> config = getOptimizedTableConfig(tableType, "volume-optimized");
        
        // 根据数据量调整配置
        if (estimatedRowsPerDay > 10_000_000) {
            // 大数据量优化
            config.put("write-buffer-size", "1gb");
            config.put("bucket", "16");
            config.put("file.target-file-size", "512MB");
            config.put("compaction.min.file-num", "20");
            log.info("📊 Applied large volume optimizations for {} rows/day", estimatedRowsPerDay);
        } else if (estimatedRowsPerDay > 1_000_000) {
            // 中等数据量优化
            config.put("write-buffer-size", "512mb");
            config.put("bucket", "8");
            config.put("file.target-file-size", "256MB");
            config.put("compaction.min.file-num", "10");
            log.info("📊 Applied medium volume optimizations for {} rows/day", estimatedRowsPerDay);
        } else {
            // 小数据量优化
            config.put("write-buffer-size", "128mb");
            config.put("bucket", "4");
            config.put("file.target-file-size", "64MB");
            config.put("compaction.min.file-num", "5");
            log.info("📊 Applied small volume optimizations for {} rows/day", estimatedRowsPerDay);
        }
        
        return config;
    }

    /**
     * 验证配置的合理性
     * 
     * @param config 配置映射
     * @return 验证结果和建议
     */
    public static String validateConfig(Map<String, String> config) {
        StringBuilder validation = new StringBuilder();
        
        // 检查写入缓冲区大小
        String writeBufferSize = config.get("write-buffer-size");
        if (writeBufferSize != null) {
            int bufferMB = Integer.parseInt(writeBufferSize.replace("mb", "").replace("gb", "000"));
            if (bufferMB < 64) {
                validation.append("⚠️ 写入缓冲区可能过小，建议至少 128MB\n");
            } else if (bufferMB > 2048) {
                validation.append("⚠️ 写入缓冲区可能过大，可能导致内存压力\n");
            }
        }
        
        // 检查分桶数量
        String bucket = config.get("bucket");
        if (bucket != null) {
            int bucketNum = Integer.parseInt(bucket);
            if (bucketNum < 2) {
                validation.append("⚠️ 分桶数量过少，可能影响并行度\n");
            } else if (bucketNum > 32) {
                validation.append("⚠️ 分桶数量过多，可能产生小文件问题\n");
            }
        }
        
        // 检查缓存配置
        String cacheTtl = config.get("lookup.cache.ttl");
        if (cacheTtl != null && cacheTtl.contains("h")) {
            int hours = Integer.parseInt(cacheTtl.replace("h", ""));
            if (hours > 24) {
                validation.append("⚠️ 缓存 TTL 过长，可能导致数据不一致\n");
            }
        }
        
        if (validation.length() == 0) {
            validation.append("✅ 配置验证通过");
        }
        
        return validation.toString();
    }
}
