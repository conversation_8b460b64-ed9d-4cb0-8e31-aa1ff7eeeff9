package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import com.ipg.olap.stream.optimizer.SqlQueryOptimizer;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * 增强的应用事实表处理器
 * 实现完整的 UNION ALL 逻辑，覆盖所有节点类型，使用时态关联优化
 */
public class ApplicationFactProcessor extends OptimizedSqlProcessor {

    private static final Logger log = LoggerFactory.getLogger(ApplicationFactProcessor.class);

    public ApplicationFactProcessor() {
        super("ApplicationFactProcessor", "dwd_apn.dwd_application_fact");
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.FACT;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "talent_recruitment_process_submit_to_job",
            "talent_recruitment_process_node",
            "talent_recruitment_process_submit_to_client",
            "talent_recruitment_process_interview",
            "talent_recruitment_process_offer",
            "talent_recruitment_process_ipg_offer_accept",
            "talent_recruitment_process_onboard",
            "talent_recruitment_process_onboard_date",
            "talent_recruitment_process_eliminate"
        };
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableDDL = """
            CREATE TABLE IF NOT EXISTS %s (
                talent_recruitment_process_id BIGINT,
                node_type INT,
                node_status INT,
                node_id BIGINT,
                progress INT,
                final_round BOOLEAN,
                add_date TIMESTAMP(3),
                event_date TIMESTAMP(3),
                last_modified_date TIMESTAMP(3),
                note_last_modify_date TIMESTAMP(3),
                dt STRING,
                operator BIGINT,
                PRIMARY KEY (talent_recruitment_process_id, node_type, node_id, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )""".formatted(targetTableName, getOptimizedPaimonTableConfig());

        // 直接执行建表 SQL，优化配置已在 WITH 子句中
        tableEnv.executeSql(createTableDDL);
        
        log.info("Optimized fact table created: {}", getTargetTableName());
        log.info("Performance info: {}", getPerformanceInfo());
    }

    /**
     * 获取优化的 Paimon 表配置字符串
     * 基于事实表的特性进行优化配置
     */
    private String getOptimizedPaimonTableConfig() {
        return String.join(",\n                ",
            "'write-buffer-size' = '512mb'",
            "'sink.parallelism' = '4'", 
            "'lookup.cache.ttl' = '1h'",
            "'lookup.cache.max-rows' = '50000'",
            "'compaction.min.file-num' = '5'",
            "'changelog-producer' = 'lookup'",
            "'precommit-compact' = 'true'",
            "'compaction.max.file-num' = '50'",
            "'snapshot.time-retained' = '1h'",
            "'bucket' = '8'"
        );
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        // 构建完整的 UNION ALL 逻辑，覆盖所有节点类型
        String completeFactSQL = buildCompleteFactUnionAllQuery();
        
        log.info("Executing complete fact processing SQL with all node types:");
        log.info(completeFactSQL);

        statementSet.addInsertSql(completeFactSQL);
    }

    /**
     * 构建完整的事实表 UNION ALL 查询
     * 包含所有节点类型：submit_to_job, submit_to_client, interview, offer, offer_accept, onboard, eliminate
     */
    private String buildCompleteFactUnionAllQuery() {
        // 构建所有子查询
        List<String> subQueries = Arrays.asList(
            buildSubmitToJobQuery(),
            buildSubmitToClientQuery(),
            buildInterviewQuery(),
            buildOfferQuery(),
            buildOfferAcceptQuery(),
            buildOnboardQuery(),
            buildEliminateQuery()
        );

        // 使用 SqlQueryOptimizer 构建优化的 UNION ALL
        String hints = getOptimizedSqlHints();
        String unionAllQuery = SqlQueryOptimizer.buildOptimizedUnionAll(subQueries, hints);
        
        return String.format("INSERT INTO %s\n%s", targetTableName, unionAllQuery);
    }

    /**
     * 构建提交到职位的子查询 (node_type = 10)
     */
    private String buildSubmitToJobQuery() {
        return """
            SELECT
                CAST(trpn.talent_recruitment_process_id AS BIGINT) AS talent_recruitment_process_id,
                CAST(trpn.node_type AS INT) AS node_type,
                CAST(trpn.node_status AS INT) AS node_status,
                CAST(submit_job.id AS BIGINT) AS node_id,
                CAST(0 AS INT) AS progress,
                CAST(TRUE AS BOOLEAN) AS final_round,
                CAST(submit_job.created_date AS TIMESTAMP(3)) AS add_date,
                CAST(submit_job.created_date AS TIMESTAMP(3)) AS event_date,
                CAST(submit_job.last_modified_date AS TIMESTAMP(3)) AS last_modified_date,
                CAST(submit_job.note_last_modified_date AS TIMESTAMP(3)) AS note_last_modify_date,
                CAST(DATE_FORMAT(submit_job.created_date, 'yyyy-MM') AS STRING) AS dt,
                CAST(submit_job.puser_id AS BIGINT) AS operator
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM ods_apn.talent_recruitment_process_submit_to_job
            ) AS submit_job
            INNER JOIN ods_apn.talent_recruitment_process_node FOR SYSTEM_TIME AS OF submit_job.proc_time AS trpn
                ON trpn.talent_recruitment_process_id = submit_job.talent_recruitment_process_id
                AND trpn.node_type = 10
            """;
    }

    /**
     * 构建提交到客户的子查询 (node_type = 20)
     */
    private String buildSubmitToClientQuery() {
        return """
            SELECT
                CAST(trpn.talent_recruitment_process_id AS BIGINT) AS talent_recruitment_process_id,
                CAST(trpn.node_type AS INT) AS node_type,
                CAST(trpn.node_status AS INT) AS node_status,
                CAST(submit_client.id AS BIGINT) AS node_id,
                CAST(0 AS INT) AS progress,
                CAST(TRUE AS BOOLEAN) AS final_round,
                CAST(submit_client.created_date AS TIMESTAMP(3)) AS add_date,
                CAST(submit_client.submit_time AS TIMESTAMP(3)) AS event_date,
                CAST(submit_client.last_modified_date AS TIMESTAMP(3)) AS last_modified_date,
                CAST(submit_client.note_last_modified_date AS TIMESTAMP(3)) AS note_last_modify_date,
                CAST(DATE_FORMAT(submit_client.created_date, 'yyyy-MM') AS STRING) AS dt,
                CAST(submit_client.puser_id AS BIGINT) AS operator
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM ods_apn.talent_recruitment_process_submit_to_client
            ) AS submit_client
            INNER JOIN ods_apn.talent_recruitment_process_node FOR SYSTEM_TIME AS OF submit_client.proc_time AS trpn
                ON trpn.talent_recruitment_process_id = submit_client.talent_recruitment_process_id
                AND trpn.node_type = 20
            """;
    }

    /**
     * 构建面试的子查询 (node_type = 30)
     */
    private String buildInterviewQuery() {
        return """
            SELECT
                CAST(trpn.talent_recruitment_process_id AS BIGINT) AS talent_recruitment_process_id,
                CAST(trpn.node_type AS INT) AS node_type,
                CAST(trpn.node_status AS INT) AS node_status,
                CAST(interview.id AS BIGINT) AS node_id,
                CAST(interview.progress AS INT) AS progress,
                CAST(interview.final_round AS BOOLEAN) AS final_round,
                CAST(interview.created_date AS TIMESTAMP(3)) AS add_date,
                CAST(interview.from_time AS TIMESTAMP(3)) AS event_date,
                CAST(interview.last_modified_date AS TIMESTAMP(3)) AS last_modified_date,
                CAST(interview.note_last_modified_date AS TIMESTAMP(3)) AS note_last_modify_date,
                CAST(DATE_FORMAT(interview.created_date, 'yyyy-MM') AS STRING) AS dt,
                CAST(interview.puser_id AS BIGINT) AS operator
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM ods_apn.talent_recruitment_process_interview
            ) AS interview
            INNER JOIN ods_apn.talent_recruitment_process_node FOR SYSTEM_TIME AS OF interview.proc_time AS trpn
                ON trpn.talent_recruitment_process_id = interview.talent_recruitment_process_id
                AND trpn.node_type = 30
            """;
    }

    /**
     * 构建录用的子查询 (node_type = 40)
     */
    private String buildOfferQuery() {
        return """
            SELECT
                CAST(trpn.talent_recruitment_process_id AS BIGINT) AS talent_recruitment_process_id,
                CAST(trpn.node_type AS INT) AS node_type,
                CAST(trpn.node_status AS INT) AS node_status,
                CAST(offer.id AS BIGINT) AS node_id,
                CAST(0 AS INT) AS progress,
                CAST(TRUE AS BOOLEAN) AS final_round,
                CAST(offer.created_date AS TIMESTAMP(3)) AS add_date,
                CAST(offer.created_date AS TIMESTAMP(3)) AS event_date,
                CAST(offer.last_modified_date AS TIMESTAMP(3)) AS last_modified_date,
                CAST(offer.note_last_modified_date AS TIMESTAMP(3)) AS note_last_modify_date,
                CAST(DATE_FORMAT(offer.created_date, 'yyyy-MM') AS STRING) AS dt,
                CAST(offer.puser_id AS BIGINT) AS operator
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM ods_apn.talent_recruitment_process_offer
            ) AS offer
            INNER JOIN ods_apn.talent_recruitment_process_node FOR SYSTEM_TIME AS OF offer.proc_time AS trpn
                ON trpn.talent_recruitment_process_id = offer.talent_recruitment_process_id
                AND trpn.node_type = 40
            """;
    }

    /**
     * 构建录用接受的子查询 (node_type = 41)
     */
    private String buildOfferAcceptQuery() {
        return """
            SELECT
                CAST(trpn.talent_recruitment_process_id AS BIGINT) AS talent_recruitment_process_id,
                CAST(trpn.node_type AS INT) AS node_type,
                CAST(trpn.node_status AS INT) AS node_status,
                CAST(offer_accept.id AS BIGINT) AS node_id,
                CAST(0 AS INT) AS progress,
                CAST(TRUE AS BOOLEAN) AS final_round,
                CAST(offer_accept.created_date AS TIMESTAMP(3)) AS add_date,
                CAST(offer_accept.created_date AS TIMESTAMP(3)) AS event_date,
                CAST(offer_accept.last_modified_date AS TIMESTAMP(3)) AS last_modified_date,
                CAST(offer_accept.note_last_modified_date AS TIMESTAMP(3)) AS note_last_modify_date,
                CAST(DATE_FORMAT(offer_accept.created_date, 'yyyy-MM') AS STRING) AS dt,
                CAST(offer_accept.puser_id AS BIGINT) AS operator
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM ods_apn.talent_recruitment_process_ipg_offer_accept
            ) AS offer_accept
            INNER JOIN ods_apn.talent_recruitment_process_node FOR SYSTEM_TIME AS OF offer_accept.proc_time AS trpn
                ON trpn.talent_recruitment_process_id = offer_accept.talent_recruitment_process_id
                AND trpn.node_type = 41
            """;
    }

    /**
     * 构建入职的子查询 (node_type = 60)
     */
    private String buildOnboardQuery() {
        return """
            SELECT
                CAST(trpn.talent_recruitment_process_id AS BIGINT) AS talent_recruitment_process_id,
                CAST(trpn.node_type AS INT) AS node_type,
                CAST(trpn.node_status AS INT) AS node_status,
                CAST(onboard.id AS BIGINT) AS node_id,
                CAST(0 AS INT) AS progress,
                CAST(TRUE AS BOOLEAN) AS final_round,
                CAST(onboard.created_date AS TIMESTAMP(3)) AS add_date,
                CAST(COALESCE(onboard_date.onboard_date, onboard.created_date) AS TIMESTAMP(3)) AS event_date,
                CAST(onboard.last_modified_date AS TIMESTAMP(3)) AS last_modified_date,
                CAST(onboard.note_last_modified_date AS TIMESTAMP(3)) AS note_last_modify_date,
                CAST(DATE_FORMAT(onboard.created_date, 'yyyy-MM') AS STRING) AS dt,
                CAST(onboard.puser_id AS BIGINT) AS operator
            FROM (
                SELECT *, PROCTIME() AS proc_time 
                FROM ods_apn.talent_recruitment_process_onboard
            ) AS onboard
            INNER JOIN ods_apn.talent_recruitment_process_node FOR SYSTEM_TIME AS OF onboard.proc_time AS trpn
                ON trpn.talent_recruitment_process_id = onboard.talent_recruitment_process_id
                AND trpn.node_type = 60
            LEFT JOIN ods_apn.talent_recruitment_process_onboard_date FOR SYSTEM_TIME AS OF onboard.proc_time AS onboard_date
                ON onboard_date.talent_recruitment_process_id = onboard.talent_recruitment_process_id
            """;
    }

    /**
     * 构建淘汰的子查询 (node_type = -1, node_status = 4)
     */
    private String buildEliminateQuery() {
        return """
            SELECT
                CAST(eliminate.talent_recruitment_process_id AS BIGINT) AS talent_recruitment_process_id,
                CAST(-1 AS INT) AS node_type,
                CAST(4 AS INT) AS node_status,
                CAST(eliminate.id AS BIGINT) AS node_id,
                CAST(0 AS INT) AS progress,
                CAST(TRUE AS BOOLEAN) AS final_round,
                CAST(eliminate.created_date AS TIMESTAMP(3)) AS add_date,
                CAST(eliminate.created_date AS TIMESTAMP(3)) AS event_date,
                CAST(eliminate.last_modified_date AS TIMESTAMP(3)) AS last_modified_date,
                CAST(eliminate.note_last_modified_date AS TIMESTAMP(3)) AS note_last_modify_date,
                CAST(DATE_FORMAT(eliminate.created_date, 'yyyy-MM') AS STRING) AS dt,
                CAST(eliminate.puser_id AS BIGINT) AS operator
            FROM ods_apn.talent_recruitment_process_eliminate AS eliminate
            """;
    }
}
