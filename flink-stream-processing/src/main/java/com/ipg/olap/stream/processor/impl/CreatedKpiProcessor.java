package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.optimizer.SqlQueryOptimizer;
import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 创建类 KPI 处理器
 * 实现 mv_created_kpi 物化视图逻辑，处理公司、职位、人才创建指标
 * 使用 BITMAP_AGG 等效聚合操作和优化的团队层级关联
 */
public class CreatedKpiProcessor extends OptimizedSqlProcessor {

    private static final String TARGET_TABLE = "dwd_apn.dwd_created_kpi";
    private static final String PROCESSOR_NAME = "CreatedKpiProcessor";

    public CreatedKpiProcessor() {
        super(PROCESSOR_NAME, TARGET_TABLE);
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.KPI;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "ods_crm.account_company",
            "ods_crm.business_flow_administrator",
            "ods_apn.company",
            "ods_apn.business_flow_administrator",
            "ods_apn.job",
            "ods_apn.job_user_relation",
            "ods_apn.talent",
            "ods_apn.talent_user_relation",
            "ods_apn.user",
            "ods_apn.permission_user_team",
            "ods_apn.permission_team",
            "dwd_apn.dwd_team_hierarchy"
        };
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableSql = String.format("""
            CREATE TABLE IF NOT EXISTS %s (
                tenant_id BIGINT,
                team_id BIGINT,
                team_name STRING,
                team_parent_id BIGINT,
                team_level INT,
                team_is_leaf BOOLEAN,
                user_id BIGINT,
                user_name STRING,
                user_activated BOOLEAN,
                add_date TIMESTAMP(3),
                event_date TIMESTAMP(3),
                entity_id BIGINT,
                created_company_count MULTISET<BIGINT>,
                upgrade_company_count MULTISET<BIGINT>,
                openings INT,
                created_talent_count MULTISET<BIGINT>,
                dt STRING,
                PRIMARY KEY (team_id, user_id, entity_id, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )
            """, TARGET_TABLE, getStandardPaimonTableConfig());

        // 直接执行建表 SQL，优化配置已在 WITH 子句中
        tableEnv.executeSql(createTableSql);
        System.out.println("✅ Created target table: " + TARGET_TABLE);
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        // 创建优化的创建类 KPI 计算 SQL
        String createdKpiSql = buildCreatedKpiQuery();
        
        System.out.println("Executing created KPI processing with optimized SQL:");
        System.out.println(createdKpiSql);
        
        statementSet.addInsertSql(createdKpiSql);
    }

    /**
     * 构建创建类 KPI 计算查询
     * 创建复杂的 UNION ALL 逻辑处理公司创建、升级、职位创建、人才创建
     * 
     * @return 优化的创建类 KPI SQL 查询
     */
    private String buildCreatedKpiQuery() {
        String hints = getOptimizedSqlHints();
        
        // 构建各个子查询
        List<String> subQueries = Arrays.asList(
            buildCreatedCompanyQuery(),
            buildUpgradeCompanyQuery(),
            buildCreatedJobQuery(),
            buildCreatedTalentQuery()
        );
        
        // 使用 CTE 构建完整查询
        Map<String, String> cteDefinitions = new HashMap<>();
        cteDefinitions.put("base_data", SqlQueryOptimizer.buildOptimizedUnionAll(subQueries));
        
        String mainQuery = String.format("""
            INSERT INTO %s
            SELECT 
                base_data.tenant_id,
                hierarchy.parent_team_id AS team_id,
                hierarchy.parent_team_name AS team_name,
                hierarchy.parent_team_parent_id AS team_parent_id,
                hierarchy.parent_team_level AS team_level,
                hierarchy.parent_team_is_leaf AS team_is_leaf,
                base_data.user_id,
                base_data.user_name,
                base_data.user_activated,
                base_data.add_date,
                base_data.event_date,
                base_data.entity_id,
                COLLECT(CASE WHEN base_data.created_company_count IS NOT NULL 
                            THEN base_data.entity_id END) AS created_company_count,
                COLLECT(CASE WHEN base_data.upgrade_company_count IS NOT NULL 
                            THEN base_data.entity_id END) AS upgrade_company_count,
                base_data.openings,
                COLLECT(CASE WHEN base_data.created_talent_count IS NOT NULL 
                            THEN base_data.entity_id END) AS created_talent_count,
                DATE_FORMAT(LOCALTIMESTAMP, 'yyyy-MM-dd') as dt
            FROM base_data
            INNER JOIN dwd_apn.dwd_team_hierarchy FOR SYSTEM_TIME AS OF PROCTIME() AS hierarchy 
                ON base_data.original_team_id = hierarchy.child_team_id
            GROUP BY 
                base_data.tenant_id,
                hierarchy.parent_team_id,
                hierarchy.parent_team_name,
                hierarchy.parent_team_parent_id,
                hierarchy.parent_team_level,
                hierarchy.parent_team_is_leaf,
                base_data.user_id,
                base_data.user_name,
                base_data.user_activated,
                base_data.add_date,
                base_data.event_date,
                base_data.entity_id,
                base_data.openings
            """, TARGET_TABLE);
        
        String completeSql = SqlQueryOptimizer.buildOptimizedMultipleCTE(cteDefinitions, mainQuery);
        
        return hints + "\n" + completeSql;
    }

    /**
     * 构建创建公司子查询
     */
    private String buildCreatedCompanyQuery() {
        return """
            SELECT 
                u.tenant_id,
                put.team_id AS original_team_id,
                u.id AS user_id,
                CONCAT(u.first_name, ' ', u.last_name) AS user_name,
                u.activated AS user_activated,
                ac.created_date AS add_date,
                ac.created_date AS event_date,
                ac.id AS entity_id,
                ac.id AS created_company_count,
                CAST(NULL AS BIGINT) AS upgrade_company_count,
                CAST(NULL AS INT) AS openings,
                CAST(NULL AS BIGINT) AS created_talent_count
            FROM ods_crm.account_company AS ac
            INNER JOIN ods_crm.business_flow_administrator AS bfa 
                ON ac.id = bfa.account_company_id
            INNER JOIN ods_apn.user AS u 
                ON bfa.user_id = u.id AND u.tenant_id = ac.tenant_id
            INNER JOIN ods_apn.permission_user_team AS put 
                ON u.id = put.user_id AND put.is_primary = 1
            """;
    }

    /**
     * 构建升级公司子查询
     */
    private String buildUpgradeCompanyQuery() {
        return """
            SELECT 
                u.tenant_id,
                put.team_id AS original_team_id,
                u.id AS user_id,
                CONCAT(u.first_name, ' ', u.last_name) AS user_name,
                u.activated AS user_activated,
                c.request_date AS add_date,
                c.request_date AS event_date,
                c.id AS entity_id,
                CAST(NULL AS BIGINT) AS created_company_count,
                c.id AS upgrade_company_count,
                CAST(NULL AS INT) AS openings,
                CAST(NULL AS BIGINT) AS created_talent_count
            FROM ods_apn.company AS c
            INNER JOIN ods_apn.business_flow_administrator AS bfa 
                ON c.id = bfa.company_id
            INNER JOIN ods_apn.user AS u 
                ON bfa.user_id = u.id AND u.tenant_id = c.tenant_id
            INNER JOIN ods_apn.permission_user_team AS put 
                ON u.id = put.user_id AND put.is_primary = 1
            WHERE c.active = 30
            """;
    }

    /**
     * 构建创建职位子查询
     */
    private String buildCreatedJobQuery() {
        return """
            SELECT 
                u.tenant_id,
                put.team_id AS original_team_id,
                u.id AS user_id,
                CONCAT(u.first_name, ' ', u.last_name) AS user_name,
                u.activated AS user_activated,
                j.created_date AS add_date,
                j.start_date AS event_date,
                j.id AS entity_id,
                CAST(NULL AS BIGINT) AS created_company_count,
                CAST(NULL AS BIGINT) AS upgrade_company_count,
                j.openings AS openings,
                CAST(NULL AS BIGINT) AS created_talent_count
            FROM ods_apn.job AS j
            INNER JOIN ods_apn.job_user_relation AS ul 
                ON ul.job_id = j.id
            INNER JOIN ods_apn.permission_user_team AS put 
                ON put.user_id = ul.user_id AND put.is_primary = 1
            INNER JOIN ods_apn.permission_team AS pt 
                ON pt.id = put.team_id
            INNER JOIN ods_apn.user AS u 
                ON u.id = ul.user_id AND u.tenant_id = j.tenant_id
            """;
    }

    /**
     * 构建创建人才子查询
     */
    private String buildCreatedTalentQuery() {
        return """
            SELECT 
                u.tenant_id,
                put.team_id AS original_team_id,
                u.id AS user_id,
                CONCAT(u.first_name, ' ', u.last_name) AS user_name,
                u.activated AS user_activated,
                t.created_date AS add_date,
                t.created_date AS event_date,
                t.id AS entity_id,
                CAST(NULL AS BIGINT) AS created_company_count,
                CAST(NULL AS BIGINT) AS upgrade_company_count,
                CAST(NULL AS INT) AS openings,
                t.id AS created_talent_count
            FROM ods_apn.talent AS t
            INNER JOIN ods_apn.talent_user_relation AS tur 
                ON t.id = tur.talent_id
            INNER JOIN ods_apn.user AS u 
                ON u.id = tur.user_id AND u.tenant_id = t.tenant_id
            INNER JOIN ods_apn.permission_user_team AS put 
                ON put.user_id = u.id AND put.is_primary = 1
            INNER JOIN ods_apn.permission_team AS pt 
                ON pt.id = put.team_id
            """;
    }

    /**
     * 获取处理器性能信息
     * 
     * @return 性能配置信息
     */
    @Override
    public String getPerformanceInfo() {
        return super.getPerformanceInfo() + 
               ", Optimization: BITMAP_AGG equivalent with COLLECT, Temporal join with team hierarchy";
    }
}