package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import com.ipg.olap.stream.optimizer.SqlQueryOptimizer;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 录用阶段处理器
 * 实现 mv_application_offer 录用阶段逻辑
 * 
 * 功能包括：
 * - 录用阶段逻辑包括小于 2 周录用指标的时间计算
 * - AI 推荐和精准度跟踪针对录用阶段的效果分析
 * - 职位状态过滤和基于日期的录用有效性检查
 */
public class OfferProcessor extends OptimizedSqlProcessor {

    private static final Logger log = LoggerFactory.getLogger(OfferProcessor.class);

    public OfferProcessor() {
        super("OfferProcessor", "dwd_application_offer");
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.AGGREGATION;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "dwd_apn.dwd_application_wide",
            "dwd_apn.dwd_application_fact",
            "ods_apn.job"
        };
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        try {
            log.info("开始处理录用阶段数据 - {}", getPerformanceInfo());

            // 构建并执行录用处理 SQL
            String offerSql = buildOfferProcessingSql();
            log.info("执行录用处理 SQL: {}", offerSql);

            statementSet.addInsertSql(offerSql);

            log.info("录用阶段数据处理完成");

        } catch (Exception e) {
            log.error("录用阶段数据处理失败", e);
            throw new RuntimeException("OfferProcessor 处理失败", e);
        }
    }

    /**
     * 创建录用阶段目标表
     */
    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableSql = """
            CREATE TABLE IF NOT EXISTS dwd_apn.dwd_application_offer (
                tenant_id BIGINT,
                company_id BIGINT,
                job_id BIGINT,
                job_pteam_id BIGINT,
                team_id BIGINT,
                team_name STRING,
                user_id BIGINT,
                user_name STRING,
                user_activated BOOLEAN,
                add_date TIMESTAMP(3),
                event_date TIMESTAMP(3),
                user_roles MULTISET<INT>,
                
                -- 录用基础指标
                offer_count_num MULTISET<BIGINT>,
                offer_ai_recommend_count_num MULTISET<BIGINT>,
                offer_precision_ai_recommend_num MULTISET<BIGINT>,
                
                -- 当前录用指标
                offer_current_count_num MULTISET<BIGINT>,
                offer_current_ai_recommend_num MULTISET<BIGINT>,
                offer_current_precision_ai_recommend_num MULTISET<BIGINT>,
                
                -- 时间相关录用指标
                offer_less_2_week_num MULTISET<BIGINT>,
                offer_current_less_2_week_num MULTISET<BIGINT>,
                
                dt STRING,
                PRIMARY KEY (tenant_id, company_id, job_id, team_id, user_id, add_date, event_date, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )
            """.formatted(getStandardPaimonConfig());

        tableEnv.executeSql(createTableSql);
        log.info("录用阶段目标表创建完成");
    }

    /**
     * 获取标准的 Paimon 表配置
     */
    private String getStandardPaimonConfig() {
        return String.join(",\n                ",
            "'write-buffer-size' = '512mb'",
            "'sink.parallelism' = '4'", 
            "'lookup.cache.ttl' = '1h'",
            "'compaction.min.file-num' = '5'",
            "'changelog-producer' = 'lookup'",
            "'precommit-compact' = 'true'",
            "'compaction.max.file-num' = '50'",
            "'snapshot.time-retained' = '1h'"
        );
    }

    /**
     * 构建录用处理 SQL
     * 实现录用阶段的复杂逻辑，包括时间计算和 AI 推荐跟踪
     */
    private String buildOfferProcessingSql() {
        // 构建 CTE 定义
        Map<String, String> cteDefinitions = new LinkedHashMap<>();

        // CTE 1: 获取提交到客户端的数据
        cteDefinitions.put("to_client_data", """
            SELECT 
                talent_recruitment_process_id,
                add_date as to_client_add_date
            FROM dwd_apn.dwd_application_fact
            WHERE node_type = 20
            """);

        // CTE 2: 获取职位状态数据
        cteDefinitions.put("job_status_data", """
            SELECT 
                id as job_id,
                status as job_status
            FROM ods_apn.job
            """);

        // 主查询：录用阶段聚合逻辑
        String mainQuery = String.format("""
            INSERT INTO dwd_apn.dwd_application_offer
            SELECT 
                application.tenant_id,
                application.company_id,
                application.job_id,
                application.job_pteam_id,
                application.team_id,
                application.team_name,
                application.user_id,
                application.user_name,
                application.user_activated,
                application.add_date,
                application.event_date,
                %s,
                
                -- 录用基础指标
                %s,
                %s,
                %s,
                
                -- 当前录用指标
                %s,
                %s,
                %s,
                
                -- 时间相关录用指标
                %s,
                %s,
                
                application.dt
            FROM dwd_apn.dwd_application_wide AS application
            LEFT JOIN to_client_data 
                ON to_client_data.talent_recruitment_process_id = application.talent_recruitment_process_id
            LEFT JOIN job_status_data j 
                ON j.job_id = application.job_id
            WHERE application.node_type = 40
            GROUP BY application.tenant_id, application.company_id, application.job_id, 
                     application.job_pteam_id, application.team_id, application.team_name, 
                     application.user_id, application.user_name, application.user_activated,
                     application.add_date, application.event_date, application.dt
            """,
            // 用户角色聚合
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.user_role", "TRUE", "user_roles"),
            
            // 录用基础指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.node_id", "TRUE", "offer_count_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "application.ai_score IS NOT NULL", "offer_ai_recommend_count_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "application.recommend_feedback_id IS NOT NULL", "offer_precision_ai_recommend_num"),
            
            // 当前录用指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.node_id", 
                "application.node_status = 1", "offer_current_count_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "application.ai_score IS NOT NULL AND application.node_status = 1", "offer_current_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "application.recommend_feedback_id IS NOT NULL AND application.node_status = 1", 
                "offer_current_precision_ai_recommend_num"),
            
            // 时间相关录用指标 - 小于2周录用
            buildOfferLess2WeekCondition("offer_less_2_week_num", false),
            buildOfferLess2WeekCondition("offer_current_less_2_week_num", true)
        );

        // 使用 CTE 构建完整 SQL
        return SqlQueryOptimizer.buildOptimizedMultipleCTE(cteDefinitions, mainQuery);
    }

    /**
     * 构建小于2周录用的条件逻辑
     * 
     * @param alias 字段别名
     * @param includeCurrent 是否包含当前状态条件
     * @return BITMAP_AGG 等效 SQL
     */
    private String buildOfferLess2WeekCondition(String alias, boolean includeCurrent) {
        String baseCondition = """
            j.job_status <> 0 
            AND to_client_data.to_client_add_date >= LOCALTIMESTAMP - INTERVAL '1' YEAR
            AND to_client_data.to_client_add_date > application.add_date - INTERVAL '2' WEEK
            """;
        
        String condition = includeCurrent ? 
            baseCondition + " AND application.node_status = 1" : 
            baseCondition;
            
        return SqlQueryOptimizer.buildBitmapAggEquivalent(
            "application.talent_recruitment_process_id", 
            condition, 
            alias
        );
    }

    /**
     * 获取处理器描述信息
     */
    @Override
    public String toString() {
        return String.format("OfferProcessor{processorType=%s, targetTable=%s}", 
                           getProcessorType(), getTargetTableName());
    }
}