package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import com.ipg.olap.stream.optimizer.SqlQueryOptimizer;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * FunnelKpiViewProcessor - 实现漏斗 KPI 视图处理器
 * 
 * 实现 view_application_funnel_kpi 的完整逻辑，包括：
 * - 创建完整的漏斗 KPI 逻辑包括所有申请阶段的历史指标
 * - 实现高效的 BITMAP_UNION 操作支持跨团队层级的漏斗分析
 * - 优化复杂聚合逻辑使用适当的窗口和状态管理策略
 * 
 * Requirements: 4.4
 */
public class FunnelKpiViewProcessor extends OptimizedSqlProcessor {

    private static final Logger log = LoggerFactory.getLogger(FunnelKpiViewProcessor.class);

    public FunnelKpiViewProcessor() {
        super("FunnelKpiViewProcessor", "view_application_funnel_kpi");
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.AGGREGATION;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "mv_application_to_job",
            "mv_application_to_client", 
            "mv_application_interview",
            "mv_application_reserve_interview",
            "mv_application_offer",
            "mv_application_offer_accept",
            "mv_application_onboard",
            "mv_application_emiminate",
            "mv_team_hierarchy"
        };
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        try {
            log.info("开始处理漏斗 KPI 视图聚合: {}", getProcessorName());

            // 构建并执行 UNION ALL 查询
            String unionAllQuery = buildFunnelKpiUnionAllQuery();
            String optimizedQuery = "INSERT INTO " + getTargetTableName() + "\n" + unionAllQuery;

            log.info("执行漏斗 KPI 视图查询，预计处理 {} 个数据源", 8);
            statementSet.addInsertSql(optimizedQuery);

            log.info("漏斗 KPI 视图处理完成: {}", getProcessorName());

        } catch (Exception e) {
            log.error("漏斗 KPI 视图处理失败: {}", getProcessorName(), e);
            throw new RuntimeException("FunnelKpiViewProcessor 处理失败", e);
        }
    }

    /**
     * 创建漏斗 KPI 视图目标表
     */
    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableSql = """
            CREATE TABLE IF NOT EXISTS %s (
                tenant_id BIGINT,
                company_id BIGINT,
                job_id BIGINT,
                job_pteam_id BIGINT,
                team_id BIGINT,
                team_name STRING,
                team_parent_id BIGINT,
                team_level INT,
                team_is_leaf BOOLEAN,
                user_id BIGINT,
                user_name STRING,
                user_activated BOOLEAN,
                add_date TIMESTAMP(3),
                event_date TIMESTAMP(3),
                user_roles MULTISET<INT>,
                submit_to_job_countNum MULTISET<BIGINT>,
                submit_to_job_aiRecommendCountNum MULTISET<BIGINT>,
                submit_to_job_precisionAiRecommendNum MULTISET<BIGINT>,
                submit_to_job_stayedOver MULTISET<BIGINT>,
                submit_to_job_stayedOver_2Month MULTISET<BIGINT>,
                submit_to_client_countNum MULTISET<BIGINT>,
                submit_to_client_aiRecommendCountNum MULTISET<BIGINT>,
                submit_to_client_precisionAiRecommendNum MULTISET<BIGINT>,
                submit_to_client_stayedOver MULTISET<BIGINT>,
                submit_to_client_stayedOver_2Month MULTISET<BIGINT>,
                interview1 MULTISET<BIGINT>,
                interview2 MULTISET<BIGINT>,
                two_or_more_interviews MULTISET<BIGINT>,
                interview_final MULTISET<BIGINT>,
                interview_total MULTISET<BIGINT>,
                unique_interview_talents MULTISET<BIGINT>,
                interview_total_process MULTISET<BIGINT>,
                interviewTotalAiRecommendNum MULTISET<BIGINT>,
                interviewTotalProcessAiRecommendNum MULTISET<BIGINT>,
                interview1AiRecommendNum MULTISET<BIGINT>,
                interview2AiRecommendNum MULTISET<BIGINT>,
                twoOrMoreInterviewsAiRecommendNum MULTISET<BIGINT>,
                interviewFinalAiRecommendNum MULTISET<BIGINT>,
                interviewTotalPrecisionAiRecommendNum MULTISET<BIGINT>,
                interviewNumProcessPrecisionAIRecommend MULTISET<BIGINT>,
                interview1PrecisionAiRecommendNum MULTISET<BIGINT>,
                interview2PrecisionAiRecommendNum MULTISET<BIGINT>,
                twoOrMoreInterviewsPrecisionAiRecommendNum MULTISET<BIGINT>,
                interviewFinalPrecisionAiRecommendNum MULTISET<BIGINT>,
                reserve_interview_total MULTISET<BIGINT>,
                reserve_interview_aiRecommendCountNum MULTISET<BIGINT>,
                reserve_interview_precisionAiRecommendNum MULTISET<BIGINT>,
                offer_countNum MULTISET<BIGINT>,
                offer_aiRecommendCountNum MULTISET<BIGINT>,
                offer_precisionAiRecommendNum MULTISET<BIGINT>,
                offerLess2WeekNum MULTISET<BIGINT>,
                offer_accept_countNum MULTISET<BIGINT>,
                offer_accept_aiRecommendCountNum MULTISET<BIGINT>,
                offer_accept_precisionAiRecommendNum MULTISET<BIGINT>,
                onboard_countNum MULTISET<BIGINT>,
                onboard_aiRecommendCountNum MULTISET<BIGINT>,
                onboard_precisionAiRecommendNum MULTISET<BIGINT>,
                eliminate_countNum MULTISET<BIGINT>,
                eliminate_aiRecommendCountNum MULTISET<BIGINT>,
                eliminate_precisionAiRecommendNum MULTISET<BIGINT>,
                dt STRING,
                PRIMARY KEY (tenant_id, company_id, job_id, team_id, user_id, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )
            """.formatted(getTargetTableName(), getOptimizedPaimonTableConfig());

        tableEnv.executeSql(createTableSql);
        log.info("创建漏斗 KPI 视图目标表: {}", getTargetTableName());
    }

    /**
     * 获取优化的 Paimon 表配置字符串
     * 基于聚合表的特性进行优化配置
     */
    private String getOptimizedPaimonTableConfig() {
        return String.join(",\n                ",
            "'write-buffer-size' = '256mb'",
            "'sink.parallelism' = '6'", 
            "'lookup.cache.ttl' = '2h'",
            "'compaction.min.file-num' = '3'",
            "'changelog-producer' = 'lookup'",
            "'precommit-compact' = 'true'",
            "'compaction.max.file-num' = '50'",
            "'snapshot.time-retained' = '1h'",
            "'bucket' = '8'",
            "'file.target-file-size' = '64MB'"
        );
    }

    /**
     * 构建漏斗 KPI 的大规模 UNION ALL 查询
     * 合并所有阶段的历史 KPI 指标，使用 BITMAP_UNION 等效的 ARRAY 聚合
     */
    private String buildFunnelKpiUnionAllQuery() {
        // 构建各个阶段的子查询
        List<String> subQueries = Arrays.asList(
            buildFunnelToJobSubQuery(),
            buildFunnelToClientSubQuery(),
            buildFunnelInterviewSubQuery(),
            buildFunnelReserveInterviewSubQuery(),
            buildFunnelOfferSubQuery(),
            buildFunnelOfferAcceptSubQuery(),
            buildFunnelOnboardSubQuery(),
            buildFunnelEliminateSubQuery()
        );

        // 使用 SqlQueryOptimizer 构建优化的 UNION ALL
        String unionQuery = SqlQueryOptimizer.buildOptimizedUnionAll(subQueries);

        // 包装在最终的聚合查询中，实现团队层级关联和 BITMAP_UNION 聚合
        return """
            SELECT 
                base.tenant_id,
                base.company_id,
                base.job_id,
                base.job_pteam_id,
                hierarchy.parent_team_id AS team_id,
                hierarchy.parent_team_name AS team_name,
                hierarchy.parent_team_parent_id AS team_parent_id,
                hierarchy.parent_team_level AS team_level,
                hierarchy.parent_team_is_leaf AS team_is_leaf,
                base.user_id,
                base.user_name,
                base.user_activated,
                base.add_date,
                base.event_date,
                ARRAY_UNION_AGG(base.user_roles) AS user_roles,
                ARRAY_UNION_AGG(base.submit_to_job_countNum) AS submit_to_job_countNum,
                ARRAY_UNION_AGG(base.submit_to_job_aiRecommendCountNum) AS submit_to_job_aiRecommendCountNum,
                ARRAY_UNION_AGG(base.submit_to_job_precisionAiRecommendNum) AS submit_to_job_precisionAiRecommendNum,
                ARRAY_UNION_AGG(base.submit_to_job_stayedOver) AS submit_to_job_stayedOver,
                ARRAY_UNION_AGG(base.submit_to_job_stayedOver_2Month) AS submit_to_job_stayedOver_2Month,
                ARRAY_UNION_AGG(base.submit_to_client_countNum) AS submit_to_client_countNum,
                ARRAY_UNION_AGG(base.submit_to_client_aiRecommendCountNum) AS submit_to_client_aiRecommendCountNum,
                ARRAY_UNION_AGG(base.submit_to_client_precisionAiRecommendNum) AS submit_to_client_precisionAiRecommendNum,
                ARRAY_UNION_AGG(base.submit_to_client_stayedOver) AS submit_to_client_stayedOver,
                ARRAY_UNION_AGG(base.submit_to_client_stayedOver_2Month) AS submit_to_client_stayedOver_2Month,
                ARRAY_UNION_AGG(base.interview1) AS interview1,
                ARRAY_UNION_AGG(base.interview2) AS interview2,
                ARRAY_UNION_AGG(base.two_or_more_interviews) AS two_or_more_interviews,
                ARRAY_UNION_AGG(base.interview_final) AS interview_final,
                ARRAY_UNION_AGG(base.interview_total) AS interview_total,
                ARRAY_UNION_AGG(base.unique_interview_talents) AS unique_interview_talents,
                ARRAY_UNION_AGG(base.interview_total_process) AS interview_total_process,
                ARRAY_UNION_AGG(base.interviewTotalAiRecommendNum) AS interviewTotalAiRecommendNum,
                ARRAY_UNION_AGG(base.interviewTotalProcessAiRecommendNum) AS interviewTotalProcessAiRecommendNum,
                ARRAY_UNION_AGG(base.interview1AiRecommendNum) AS interview1AiRecommendNum,
                ARRAY_UNION_AGG(base.interview2AiRecommendNum) AS interview2AiRecommendNum,
                ARRAY_UNION_AGG(base.twoOrMoreInterviewsAiRecommendNum) AS twoOrMoreInterviewsAiRecommendNum,
                ARRAY_UNION_AGG(base.interviewFinalAiRecommendNum) AS interviewFinalAiRecommendNum,
                ARRAY_UNION_AGG(base.interviewTotalPrecisionAiRecommendNum) AS interviewTotalPrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.interviewNumProcessPrecisionAIRecommend) AS interviewNumProcessPrecisionAIRecommend,
                ARRAY_UNION_AGG(base.interview1PrecisionAiRecommendNum) AS interview1PrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.interview2PrecisionAiRecommendNum) AS interview2PrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.twoOrMoreInterviewsPrecisionAiRecommendNum) AS twoOrMoreInterviewsPrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.interviewFinalPrecisionAiRecommendNum) AS interviewFinalPrecisionAiRecommendNum,
                ARRAY_UNION_AGG(base.reserve_interview_total) AS reserve_interview_total,
                ARRAY_UNION_AGG(base.reserve_interview_aiRecommendCountNum) AS reserve_interview_aiRecommendCountNum,
                ARRAY_UNION_AGG(base.reserve_interview_precisionAiRecommendNum) AS reserve_interview_precisionAiRecommendNum,
                ARRAY_UNION_AGG(base.offer_countNum) AS offer_countNum,
                ARRAY_UNION_AGG(base.offer_aiRecommendCountNum) AS offer_aiRecommendCountNum,
                ARRAY_UNION_AGG(base.offer_precisionAiRecommendNum) AS offer_precisionAiRecommendNum,
                ARRAY_UNION_AGG(base.offerLess2WeekNum) AS offerLess2WeekNum,
                ARRAY_UNION_AGG(base.offer_accept_countNum) AS offer_accept_countNum,
                ARRAY_UNION_AGG(base.offer_accept_aiRecommendCountNum) AS offer_accept_aiRecommendCountNum,
                ARRAY_UNION_AGG(base.offer_accept_precisionAiRecommendNum) AS offer_accept_precisionAiRecommendNum,
                ARRAY_UNION_AGG(base.onboard_countNum) AS onboard_countNum,
                ARRAY_UNION_AGG(base.onboard_aiRecommendCountNum) AS onboard_aiRecommendCountNum,
                ARRAY_UNION_AGG(base.onboard_precisionAiRecommendNum) AS onboard_precisionAiRecommendNum,
                ARRAY_UNION_AGG(base.eliminate_countNum) AS eliminate_countNum,
                ARRAY_UNION_AGG(base.eliminate_aiRecommendCountNum) AS eliminate_aiRecommendCountNum,
                ARRAY_UNION_AGG(base.eliminate_precisionAiRecommendNum) AS eliminate_precisionAiRecommendNum,
                base.dt
            FROM (
                %s
            ) AS base
            LEFT JOIN mv_team_hierarchy FOR SYSTEM_TIME AS OF base.proc_time AS hierarchy
                ON base.team_id = hierarchy.team_id
            GROUP BY 
                base.tenant_id, base.company_id, base.job_id, base.job_pteam_id,
                hierarchy.parent_team_id, hierarchy.parent_team_name, 
                hierarchy.parent_team_parent_id, hierarchy.parent_team_level, hierarchy.parent_team_is_leaf,
                base.user_id, base.user_name, base.user_activated,
                base.add_date, base.event_date, base.dt
            """.formatted(unionQuery);
    } 
   /**
     * 构建漏斗提交到职位阶段的子查询 - 历史数据
     */
    private String buildFunnelToJobSubQuery() {
        return """
            SELECT 
                tenant_id, company_id, job_id, job_pteam_id, team_id, team_name,
                user_id, user_name, user_activated,
                add_date, event_date, PROCTIME() AS proc_time,
                user_roles,
                submit_to_job_countNum,
                submit_to_job_aiRecommendCountNum,
                submit_to_job_precisionAiRecommendNum,
                submit_to_job_stayedOver,
                submit_to_job_stayedOver_2Month,
                ARRAY[] AS submit_to_client_countNum,
                ARRAY[] AS submit_to_client_aiRecommendCountNum,
                ARRAY[] AS submit_to_client_precisionAiRecommendNum,
                ARRAY[] AS submit_to_client_stayedOver,
                ARRAY[] AS submit_to_client_stayedOver_2Month,
                ARRAY[] AS interview1,
                ARRAY[] AS interview2,
                ARRAY[] AS two_or_more_interviews,
                ARRAY[] AS interview_final,
                ARRAY[] AS interview_total,
                ARRAY[] AS unique_interview_talents,
                ARRAY[] AS interview_total_process,
                ARRAY[] AS interviewTotalAiRecommendNum,
                ARRAY[] AS interviewTotalProcessAiRecommendNum,
                ARRAY[] AS interview1AiRecommendNum,
                ARRAY[] AS interview2AiRecommendNum,
                ARRAY[] AS twoOrMoreInterviewsAiRecommendNum,
                ARRAY[] AS interviewFinalAiRecommendNum,
                ARRAY[] AS interviewTotalPrecisionAiRecommendNum,
                ARRAY[] AS interviewNumProcessPrecisionAIRecommend,
                ARRAY[] AS interview1PrecisionAiRecommendNum,
                ARRAY[] AS interview2PrecisionAiRecommendNum,
                ARRAY[] AS twoOrMoreInterviewsPrecisionAiRecommendNum,
                ARRAY[] AS interviewFinalPrecisionAiRecommendNum,
                ARRAY[] AS reserve_interview_total,
                ARRAY[] AS reserve_interview_aiRecommendCountNum,
                ARRAY[] AS reserve_interview_precisionAiRecommendNum,
                ARRAY[] AS offer_countNum,
                ARRAY[] AS offer_aiRecommendCountNum,
                ARRAY[] AS offer_precisionAiRecommendNum,
                ARRAY[] AS offerLess2WeekNum,
                ARRAY[] AS offer_accept_countNum,
                ARRAY[] AS offer_accept_aiRecommendCountNum,
                ARRAY[] AS offer_accept_precisionAiRecommendNum,
                ARRAY[] AS onboard_countNum,
                ARRAY[] AS onboard_aiRecommendCountNum,
                ARRAY[] AS onboard_precisionAiRecommendNum,
                ARRAY[] AS eliminate_countNum,
                ARRAY[] AS eliminate_aiRecommendCountNum,
                ARRAY[] AS eliminate_precisionAiRecommendNum,
                dt
            FROM mv_application_to_job
            """;
    }

    /**
     * 构建漏斗提交到客户阶段的子查询 - 历史数据
     */
    private String buildFunnelToClientSubQuery() {
        return """
            SELECT 
                tenant_id, company_id, job_id, job_pteam_id, team_id, team_name,
                user_id, user_name, user_activated,
                add_date, event_date, PROCTIME() AS proc_time,
                user_roles,
                ARRAY[] AS submit_to_job_countNum,
                ARRAY[] AS submit_to_job_aiRecommendCountNum,
                ARRAY[] AS submit_to_job_precisionAiRecommendNum,
                ARRAY[] AS submit_to_job_stayedOver,
                ARRAY[] AS submit_to_job_stayedOver_2Month,
                submit_to_client_countNum,
                submit_to_client_aiRecommendCountNum,
                submit_to_client_precisionAiRecommendNum,
                submit_to_client_stayedOver,
                submit_to_client_stayedOver_2Month,
                ARRAY[] AS interview1,
                ARRAY[] AS interview2,
                ARRAY[] AS two_or_more_interviews,
                ARRAY[] AS interview_final,
                ARRAY[] AS interview_total,
                ARRAY[] AS unique_interview_talents,
                ARRAY[] AS interview_total_process,
                ARRAY[] AS interviewTotalAiRecommendNum,
                ARRAY[] AS interviewTotalProcessAiRecommendNum,
                ARRAY[] AS interview1AiRecommendNum,
                ARRAY[] AS interview2AiRecommendNum,
                ARRAY[] AS twoOrMoreInterviewsAiRecommendNum,
                ARRAY[] AS interviewFinalAiRecommendNum,
                ARRAY[] AS interviewTotalPrecisionAiRecommendNum,
                ARRAY[] AS interviewNumProcessPrecisionAIRecommend,
                ARRAY[] AS interview1PrecisionAiRecommendNum,
                ARRAY[] AS interview2PrecisionAiRecommendNum,
                ARRAY[] AS twoOrMoreInterviewsPrecisionAiRecommendNum,
                ARRAY[] AS interviewFinalPrecisionAiRecommendNum,
                ARRAY[] AS reserve_interview_total,
                ARRAY[] AS reserve_interview_aiRecommendCountNum,
                ARRAY[] AS reserve_interview_precisionAiRecommendNum,
                ARRAY[] AS offer_countNum,
                ARRAY[] AS offer_aiRecommendCountNum,
                ARRAY[] AS offer_precisionAiRecommendNum,
                ARRAY[] AS offerLess2WeekNum,
                ARRAY[] AS offer_accept_countNum,
                ARRAY[] AS offer_accept_aiRecommendCountNum,
                ARRAY[] AS offer_accept_precisionAiRecommendNum,
                ARRAY[] AS onboard_countNum,
                ARRAY[] AS onboard_aiRecommendCountNum,
                ARRAY[] AS onboard_precisionAiRecommendNum,
                ARRAY[] AS eliminate_countNum,
                ARRAY[] AS eliminate_aiRecommendCountNum,
                ARRAY[] AS eliminate_precisionAiRecommendNum,
                dt
            FROM mv_application_to_client
            """;
    } 
   /**
     * 构建漏斗面试阶段的子查询 - 历史数据
     */
    private String buildFunnelInterviewSubQuery() {
        return """
            SELECT 
                tenant_id, company_id, job_id, job_pteam_id, team_id, team_name,
                user_id, user_name, user_activated,
                add_date, event_date, PROCTIME() AS proc_time,
                user_roles,
                ARRAY[] AS submit_to_job_countNum,
                ARRAY[] AS submit_to_job_aiRecommendCountNum,
                ARRAY[] AS submit_to_job_precisionAiRecommendNum,
                ARRAY[] AS submit_to_job_stayedOver,
                ARRAY[] AS submit_to_job_stayedOver_2Month,
                ARRAY[] AS submit_to_client_countNum,
                ARRAY[] AS submit_to_client_aiRecommendCountNum,
                ARRAY[] AS submit_to_client_precisionAiRecommendNum,
                ARRAY[] AS submit_to_client_stayedOver,
                ARRAY[] AS submit_to_client_stayedOver_2Month,
                interview1,
                interview2,
                two_or_more_interviews,
                interview_final,
                interview_total,
                unique_interview_talents,
                interview_total_process,
                interviewTotalAiRecommendNum,
                interviewTotalProcessAiRecommendNum,
                interview1AiRecommendNum,
                interview2AiRecommendNum,
                twoOrMoreInterviewsAiRecommendNum,
                interviewFinalAiRecommendNum,
                interviewTotalPrecisionAiRecommendNum,
                interviewNumProcessPrecisionAIRecommend,
                interview1PrecisionAiRecommendNum,
                interview2PrecisionAiRecommendNum,
                twoOrMoreInterviewsPrecisionAiRecommendNum,
                interviewFinalPrecisionAiRecommendNum,
                ARRAY[] AS reserve_interview_total,
                ARRAY[] AS reserve_interview_aiRecommendCountNum,
                ARRAY[] AS reserve_interview_precisionAiRecommendNum,
                ARRAY[] AS offer_countNum,
                ARRAY[] AS offer_aiRecommendCountNum,
                ARRAY[] AS offer_precisionAiRecommendNum,
                ARRAY[] AS offerLess2WeekNum,
                ARRAY[] AS offer_accept_countNum,
                ARRAY[] AS offer_accept_aiRecommendCountNum,
                ARRAY[] AS offer_accept_precisionAiRecommendNum,
                ARRAY[] AS onboard_countNum,
                ARRAY[] AS onboard_aiRecommendCountNum,
                ARRAY[] AS onboard_precisionAiRecommendNum,
                ARRAY[] AS eliminate_countNum,
                ARRAY[] AS eliminate_aiRecommendCountNum,
                ARRAY[] AS eliminate_precisionAiRecommendNum,
                dt
            FROM mv_application_interview
            """;
    }    
private String buildFunnelReserveInterviewSubQuery() {
        return "SELECT tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated, add_date, event_date, PROCTIME() AS proc_time, user_roles, ARRAY[] AS submit_to_job_countNum, ARRAY[] AS submit_to_job_aiRecommendCountNum, ARRAY[] AS submit_to_job_precisionAiRecommendNum, ARRAY[] AS submit_to_job_stayedOver, ARRAY[] AS submit_to_job_stayedOver_2Month, ARRAY[] AS submit_to_client_countNum, ARRAY[] AS submit_to_client_aiRecommendCountNum, ARRAY[] AS submit_to_client_precisionAiRecommendNum, ARRAY[] AS submit_to_client_stayedOver, ARRAY[] AS submit_to_client_stayedOver_2Month, ARRAY[] AS interview1, ARRAY[] AS interview2, ARRAY[] AS two_or_more_interviews, ARRAY[] AS interview_final, ARRAY[] AS interview_total, ARRAY[] AS unique_interview_talents, ARRAY[] AS interview_total_process, ARRAY[] AS interviewTotalAiRecommendNum, ARRAY[] AS interviewTotalProcessAiRecommendNum, ARRAY[] AS interview1AiRecommendNum, ARRAY[] AS interview2AiRecommendNum, ARRAY[] AS twoOrMoreInterviewsAiRecommendNum, ARRAY[] AS interviewFinalAiRecommendNum, ARRAY[] AS interviewTotalPrecisionAiRecommendNum, ARRAY[] AS interviewNumProcessPrecisionAIRecommend, ARRAY[] AS interview1PrecisionAiRecommendNum, ARRAY[] AS interview2PrecisionAiRecommendNum, ARRAY[] AS twoOrMoreInterviewsPrecisionAiRecommendNum, ARRAY[] AS interviewFinalPrecisionAiRecommendNum, reserve_interview_total, reserve_interview_aiRecommendCountNum, reserve_interview_precisionAiRecommendNum, ARRAY[] AS offer_countNum, ARRAY[] AS offer_aiRecommendCountNum, ARRAY[] AS offer_precisionAiRecommendNum, ARRAY[] AS offerLess2WeekNum, ARRAY[] AS offer_accept_countNum, ARRAY[] AS offer_accept_aiRecommendCountNum, ARRAY[] AS offer_accept_precisionAiRecommendNum, ARRAY[] AS onboard_countNum, ARRAY[] AS onboard_aiRecommendCountNum, ARRAY[] AS onboard_precisionAiRecommendNum, ARRAY[] AS eliminate_countNum, ARRAY[] AS eliminate_aiRecommendCountNum, ARRAY[] AS eliminate_precisionAiRecommendNum, dt FROM mv_application_reserve_interview";
    }

    private String buildFunnelOfferSubQuery() {
        return "SELECT tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated, add_date, event_date, PROCTIME() AS proc_time, user_roles, ARRAY[] AS submit_to_job_countNum, ARRAY[] AS submit_to_job_aiRecommendCountNum, ARRAY[] AS submit_to_job_precisionAiRecommendNum, ARRAY[] AS submit_to_job_stayedOver, ARRAY[] AS submit_to_job_stayedOver_2Month, ARRAY[] AS submit_to_client_countNum, ARRAY[] AS submit_to_client_aiRecommendCountNum, ARRAY[] AS submit_to_client_precisionAiRecommendNum, ARRAY[] AS submit_to_client_stayedOver, ARRAY[] AS submit_to_client_stayedOver_2Month, ARRAY[] AS interview1, ARRAY[] AS interview2, ARRAY[] AS two_or_more_interviews, ARRAY[] AS interview_final, ARRAY[] AS interview_total, ARRAY[] AS unique_interview_talents, ARRAY[] AS interview_total_process, ARRAY[] AS interviewTotalAiRecommendNum, ARRAY[] AS interviewTotalProcessAiRecommendNum, ARRAY[] AS interview1AiRecommendNum, ARRAY[] AS interview2AiRecommendNum, ARRAY[] AS twoOrMoreInterviewsAiRecommendNum, ARRAY[] AS interviewFinalAiRecommendNum, ARRAY[] AS interviewTotalPrecisionAiRecommendNum, ARRAY[] AS interviewNumProcessPrecisionAIRecommend, ARRAY[] AS interview1PrecisionAiRecommendNum, ARRAY[] AS interview2PrecisionAiRecommendNum, ARRAY[] AS twoOrMoreInterviewsPrecisionAiRecommendNum, ARRAY[] AS interviewFinalPrecisionAiRecommendNum, ARRAY[] AS reserve_interview_total, ARRAY[] AS reserve_interview_aiRecommendCountNum, ARRAY[] AS reserve_interview_precisionAiRecommendNum, offer_countNum, offer_aiRecommendCountNum, offer_precisionAiRecommendNum, offerLess2WeekNum, ARRAY[] AS offer_accept_countNum, ARRAY[] AS offer_accept_aiRecommendCountNum, ARRAY[] AS offer_accept_precisionAiRecommendNum, ARRAY[] AS onboard_countNum, ARRAY[] AS onboard_aiRecommendCountNum, ARRAY[] AS onboard_precisionAiRecommendNum, ARRAY[] AS eliminate_countNum, ARRAY[] AS eliminate_aiRecommendCountNum, ARRAY[] AS eliminate_precisionAiRecommendNum, dt FROM mv_application_offer";
    }

    private String buildFunnelOfferAcceptSubQuery() {
        return "SELECT tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated, add_date, event_date, PROCTIME() AS proc_time, user_roles, ARRAY[] AS submit_to_job_countNum, ARRAY[] AS submit_to_job_aiRecommendCountNum, ARRAY[] AS submit_to_job_precisionAiRecommendNum, ARRAY[] AS submit_to_job_stayedOver, ARRAY[] AS submit_to_job_stayedOver_2Month, ARRAY[] AS submit_to_client_countNum, ARRAY[] AS submit_to_client_aiRecommendCountNum, ARRAY[] AS submit_to_client_precisionAiRecommendNum, ARRAY[] AS submit_to_client_stayedOver, ARRAY[] AS submit_to_client_stayedOver_2Month, ARRAY[] AS interview1, ARRAY[] AS interview2, ARRAY[] AS two_or_more_interviews, ARRAY[] AS interview_final, ARRAY[] AS interview_total, ARRAY[] AS unique_interview_talents, ARRAY[] AS interview_total_process, ARRAY[] AS interviewTotalAiRecommendNum, ARRAY[] AS interviewTotalProcessAiRecommendNum, ARRAY[] AS interview1AiRecommendNum, ARRAY[] AS interview2AiRecommendNum, ARRAY[] AS twoOrMoreInterviewsAiRecommendNum, ARRAY[] AS interviewFinalAiRecommendNum, ARRAY[] AS interviewTotalPrecisionAiRecommendNum, ARRAY[] AS interviewNumProcessPrecisionAIRecommend, ARRAY[] AS interview1PrecisionAiRecommendNum, ARRAY[] AS interview2PrecisionAiRecommendNum, ARRAY[] AS twoOrMoreInterviewsPrecisionAiRecommendNum, ARRAY[] AS interviewFinalPrecisionAiRecommendNum, ARRAY[] AS reserve_interview_total, ARRAY[] AS reserve_interview_aiRecommendCountNum, ARRAY[] AS reserve_interview_precisionAiRecommendNum, ARRAY[] AS offer_countNum, ARRAY[] AS offer_aiRecommendCountNum, ARRAY[] AS offer_precisionAiRecommendNum, ARRAY[] AS offerLess2WeekNum, offer_accept_countNum, offer_accept_aiRecommendCountNum, offer_accept_precisionAiRecommendNum, ARRAY[] AS onboard_countNum, ARRAY[] AS onboard_aiRecommendCountNum, ARRAY[] AS onboard_precisionAiRecommendNum, ARRAY[] AS eliminate_countNum, ARRAY[] AS eliminate_aiRecommendCountNum, ARRAY[] AS eliminate_precisionAiRecommendNum, dt FROM mv_application_offer_accept";
    }

    private String buildFunnelOnboardSubQuery() {
        return "SELECT tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated, add_date, event_date, PROCTIME() AS proc_time, user_roles, ARRAY[] AS submit_to_job_countNum, ARRAY[] AS submit_to_job_aiRecommendCountNum, ARRAY[] AS submit_to_job_precisionAiRecommendNum, ARRAY[] AS submit_to_job_stayedOver, ARRAY[] AS submit_to_job_stayedOver_2Month, ARRAY[] AS submit_to_client_countNum, ARRAY[] AS submit_to_client_aiRecommendCountNum, ARRAY[] AS submit_to_client_precisionAiRecommendNum, ARRAY[] AS submit_to_client_stayedOver, ARRAY[] AS submit_to_client_stayedOver_2Month, ARRAY[] AS interview1, ARRAY[] AS interview2, ARRAY[] AS two_or_more_interviews, ARRAY[] AS interview_final, ARRAY[] AS interview_total, ARRAY[] AS unique_interview_talents, ARRAY[] AS interview_total_process, ARRAY[] AS interviewTotalAiRecommendNum, ARRAY[] AS interviewTotalProcessAiRecommendNum, ARRAY[] AS interview1AiRecommendNum, ARRAY[] AS interview2AiRecommendNum, ARRAY[] AS twoOrMoreInterviewsAiRecommendNum, ARRAY[] AS interviewFinalAiRecommendNum, ARRAY[] AS interviewTotalPrecisionAiRecommendNum, ARRAY[] AS interviewNumProcessPrecisionAIRecommend, ARRAY[] AS interview1PrecisionAiRecommendNum, ARRAY[] AS interview2PrecisionAiRecommendNum, ARRAY[] AS twoOrMoreInterviewsPrecisionAiRecommendNum, ARRAY[] AS interviewFinalPrecisionAiRecommendNum, ARRAY[] AS reserve_interview_total, ARRAY[] AS reserve_interview_aiRecommendCountNum, ARRAY[] AS reserve_interview_precisionAiRecommendNum, ARRAY[] AS offer_countNum, ARRAY[] AS offer_aiRecommendCountNum, ARRAY[] AS offer_precisionAiRecommendNum, ARRAY[] AS offerLess2WeekNum, ARRAY[] AS offer_accept_countNum, ARRAY[] AS offer_accept_aiRecommendCountNum, ARRAY[] AS offer_accept_precisionAiRecommendNum, onboard_countNum, onboard_aiRecommendCountNum, onboard_precisionAiRecommendNum, ARRAY[] AS eliminate_countNum, ARRAY[] AS eliminate_aiRecommendCountNum, ARRAY[] AS eliminate_precisionAiRecommendNum, dt FROM mv_application_onboard";
    }

    private String buildFunnelEliminateSubQuery() {
        return "SELECT tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated, add_date, event_date, PROCTIME() AS proc_time, user_roles, ARRAY[] AS submit_to_job_countNum, ARRAY[] AS submit_to_job_aiRecommendCountNum, ARRAY[] AS submit_to_job_precisionAiRecommendNum, ARRAY[] AS submit_to_job_stayedOver, ARRAY[] AS submit_to_job_stayedOver_2Month, ARRAY[] AS submit_to_client_countNum, ARRAY[] AS submit_to_client_aiRecommendCountNum, ARRAY[] AS submit_to_client_precisionAiRecommendNum, ARRAY[] AS submit_to_client_stayedOver, ARRAY[] AS submit_to_client_stayedOver_2Month, ARRAY[] AS interview1, ARRAY[] AS interview2, ARRAY[] AS two_or_more_interviews, ARRAY[] AS interview_final, ARRAY[] AS interview_total, ARRAY[] AS unique_interview_talents, ARRAY[] AS interview_total_process, ARRAY[] AS interviewTotalAiRecommendNum, ARRAY[] AS interviewTotalProcessAiRecommendNum, ARRAY[] AS interview1AiRecommendNum, ARRAY[] AS interview2AiRecommendNum, ARRAY[] AS twoOrMoreInterviewsAiRecommendNum, ARRAY[] AS interviewFinalAiRecommendNum, ARRAY[] AS interviewTotalPrecisionAiRecommendNum, ARRAY[] AS interviewNumProcessPrecisionAIRecommend, ARRAY[] AS interview1PrecisionAiRecommendNum, ARRAY[] AS interview2PrecisionAiRecommendNum, ARRAY[] AS twoOrMoreInterviewsPrecisionAiRecommendNum, ARRAY[] AS interviewFinalPrecisionAiRecommendNum, ARRAY[] AS reserve_interview_total, ARRAY[] AS reserve_interview_aiRecommendCountNum, ARRAY[] AS reserve_interview_precisionAiRecommendNum, ARRAY[] AS offer_countNum, ARRAY[] AS offer_aiRecommendCountNum, ARRAY[] AS offer_precisionAiRecommendNum, ARRAY[] AS offerLess2WeekNum, ARRAY[] AS offer_accept_countNum, ARRAY[] AS offer_accept_aiRecommendCountNum, ARRAY[] AS offer_accept_precisionAiRecommendNum, ARRAY[] AS onboard_countNum, ARRAY[] AS onboard_aiRecommendCountNum, ARRAY[] AS onboard_precisionAiRecommendNum, eliminate_countNum, eliminate_aiRecommendCountNum, eliminate_precisionAiRecommendNum, dt FROM mv_application_emiminate";
    }
}