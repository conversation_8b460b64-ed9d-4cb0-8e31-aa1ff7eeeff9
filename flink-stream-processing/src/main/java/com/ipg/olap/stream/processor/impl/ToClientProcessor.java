package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import com.ipg.olap.stream.optimizer.SqlQueryOptimizer;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 提交到客户端阶段处理器
 * 实现 mv_application_to_client 逻辑
 * 
 * 功能包括：
 * - 复杂的滞留计算和职位状态过滤
 * - AI 推荐和精准度跟踪
 * - 基于时间的计算使用 Flink SQL 时间函数和窗口操作
 */
public class ToClientProcessor extends OptimizedSqlProcessor {

    private static final Logger log = LoggerFactory.getLogger(ToClientProcessor.class);

    public ToClientProcessor() {
        super("ToClientProcessor", "dwd_application_to_client");
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.AGGREGATION;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "dwd_apn.dwd_application_wide",
            "dwd_apn.dwd_application_fact",
            "ods_apn.job"
        };
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        try {
            log.info("开始处理提交到客户端阶段数据 - {}", getPerformanceInfo());

            // 构建并执行提交到客户端处理 SQL
            String toClientSql = buildToClientProcessingSql();
            log.info("执行提交到客户端处理 SQL: {}", toClientSql);

            statementSet.addInsertSql(toClientSql);

            log.info("提交到客户端阶段数据处理完成");

        } catch (Exception e) {
            log.error("提交到客户端阶段数据处理失败", e);
            throw new RuntimeException("ToClientProcessor 处理失败", e);
        }
    }

    /**
     * 创建提交到客户端阶段目标表
     */
    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableSql = """
            CREATE TABLE IF NOT EXISTS dwd_apn.dwd_application_to_client (
                tenant_id BIGINT,
                company_id BIGINT,
                job_id BIGINT,
                job_pteam_id BIGINT,
                team_id BIGINT,
                team_name STRING,
                user_id BIGINT,
                user_name STRING,
                user_activated BOOLEAN,
                add_date TIMESTAMP(3),
                event_date TIMESTAMP(3),
                user_roles MULTISET<INT>,
                
                -- 提交到客户端基础指标
                submit_to_client_count_num MULTISET<BIGINT>,
                submit_to_client_ai_recommend_count_num MULTISET<BIGINT>,
                submit_to_client_precision_ai_recommend_num MULTISET<BIGINT>,
                
                -- 滞留指标
                submit_to_client_stayed_over MULTISET<BIGINT>,
                submit_to_client_stayed_over_2_month MULTISET<BIGINT>,
                
                -- 当前提交到客户端指标
                submit_to_client_current_count_num MULTISET<BIGINT>,
                submit_to_client_current_ai_recommend_num MULTISET<BIGINT>,
                submit_to_client_current_precision_ai_recommend_num MULTISET<BIGINT>,
                
                -- 当前滞留指标
                submit_to_client_current_stayed_over MULTISET<BIGINT>,
                submit_to_client_current_stayed_over_2_month MULTISET<BIGINT>,
                
                dt STRING,
                PRIMARY KEY (tenant_id, company_id, job_id, team_id, user_id, add_date, event_date, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )
            """.formatted(getStandardPaimonConfig());

        tableEnv.executeSql(createTableSql);
        log.info("提交到客户端阶段目标表创建完成");
    }

    /**
     * 获取标准的 Paimon 表配置
     */
    private String getStandardPaimonConfig() {
        return String.join(",\n                ",
            "'write-buffer-size' = '512mb'",
            "'sink.parallelism' = '4'", 
            "'lookup.cache.ttl' = '1h'",
            "'compaction.min.file-num' = '5'",
            "'changelog-producer' = 'lookup'",
            "'precommit-compact' = 'true'",
            "'compaction.max.file-num' = '50'",
            "'snapshot.time-retained' = '1h'"
        );
    }

    /**
     * 构建提交到客户端处理 SQL
     * 实现复杂的滞留计算和职位状态过滤逻辑
     */
    private String buildToClientProcessingSql() {
        // 构建 CTE 定义
        Map<String, String> cteDefinitions = new LinkedHashMap<>();

        // CTE 1: 计算滞留时间数据 (针对客户端阶段，node_type > 20)
        cteDefinitions.put("stayed_time_data", """
            SELECT 
                pn.talent_recruitment_process_id,
                CASE 
                    WHEN MIN(next.add_date) IS NOT NULL THEN MIN(next.add_date)
                    ELSE MAX(eli.add_date)
                END AS stayed_add_date
            FROM dwd_apn.dwd_application_fact pn
            LEFT JOIN dwd_apn.dwd_application_fact next 
                ON next.talent_recruitment_process_id = pn.talent_recruitment_process_id 
                AND next.node_type > 20
            LEFT JOIN dwd_apn.dwd_application_fact eli 
                ON eli.talent_recruitment_process_id = pn.talent_recruitment_process_id 
                AND eli.node_type = -1
            WHERE pn.node_type = 20
            GROUP BY pn.talent_recruitment_process_id
            """);

        // CTE 2: 获取职位状态数据
        cteDefinitions.put("job_status_data", """
            SELECT 
                id as job_id,
                status as job_status
            FROM ods_apn.job
            """);

        // 主查询：提交到客户端阶段聚合逻辑
        String mainQuery = String.format("""
            INSERT INTO dwd_apn.dwd_application_to_client
            SELECT 
                application.tenant_id,
                application.company_id,
                application.job_id,
                application.job_pteam_id,
                application.team_id,
                application.team_name,
                application.user_id,
                application.user_name,
                application.user_activated,
                application.add_date,
                application.event_date,
                %s,
                
                -- 提交到客户端基础指标
                %s,
                %s,
                %s,
                
                -- 滞留指标
                %s,
                %s,
                
                -- 当前提交到客户端指标
                %s,
                %s,
                %s,
                
                -- 当前滞留指标
                %s,
                %s,
                
                application.dt
            FROM dwd_apn.dwd_application_wide AS application
            LEFT JOIN stayed_time_data pn 
                ON pn.talent_recruitment_process_id = application.talent_recruitment_process_id
            LEFT JOIN job_status_data j 
                ON j.job_id = application.job_id
            WHERE application.node_type = 20
            GROUP BY application.tenant_id, application.company_id, application.job_id, 
                     application.job_pteam_id, application.team_id, application.team_name, 
                     application.user_id, application.user_name, application.user_activated,
                     application.add_date, application.event_date, application.dt
            """,
            // 用户角色聚合
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.user_role", "TRUE", "user_roles"),
            
            // 提交到客户端基础指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.node_id", "TRUE", "submit_to_client_count_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "application.ai_score IS NOT NULL", "submit_to_client_ai_recommend_count_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "application.recommend_feedback_id IS NOT NULL", "submit_to_client_precision_ai_recommend_num"),
            
            // 滞留指标 - 72小时和2个月 (客户端阶段使用不同的时间阈值)
            buildStayedOverCondition("submit_to_client_stayed_over", 72, false),
            buildStayedOverCondition("submit_to_client_stayed_over_2_month", 1440, false),
            
            // 当前提交到客户端指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.node_id", 
                "application.node_status = 1", "submit_to_client_current_count_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "application.ai_score IS NOT NULL AND application.node_status = 1", 
                "submit_to_client_current_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "application.recommend_feedback_id IS NOT NULL AND application.node_status = 1", 
                "submit_to_client_current_precision_ai_recommend_num"),
            
            // 当前滞留指标
            buildStayedOverCondition("submit_to_client_current_stayed_over", 72, true),
            buildStayedOverCondition("submit_to_client_current_stayed_over_2_month", 1440, true)
        );

        // 使用 CTE 构建完整 SQL
        return SqlQueryOptimizer.buildOptimizedMultipleCTE(cteDefinitions, mainQuery);
    }

    /**
     * 构建滞留时间条件逻辑
     * 客户端阶段使用72小时作为基础阈值，而不是24小时
     * 
     * @param alias 字段别名
     * @param hoursThreshold 小时阈值
     * @param includeCurrent 是否包含当前状态条件
     * @return BITMAP_AGG 等效 SQL
     */
    private String buildStayedOverCondition(String alias, int hoursThreshold, boolean includeCurrent) {
        String baseCondition = String.format("""
            j.job_status <> 0 
            AND application.add_date >= LOCALTIMESTAMP - INTERVAL '1' YEAR
            AND (
                (pn.stayed_add_date IS NOT NULL 
                 AND EXTRACT(EPOCH FROM (pn.stayed_add_date - application.add_date)) / 3600 > %d)
                OR 
                (application.node_status = 1 
                 AND EXTRACT(EPOCH FROM (LOCALTIMESTAMP - GREATEST(
                     application.add_date, 
                     application.last_modified_date, 
                     application.note_last_modify_date
                 ))) / 3600 > %d)
            )
            """, hoursThreshold, hoursThreshold);
        
        String condition = includeCurrent ? 
            baseCondition + " AND application.node_status = 1" : 
            baseCondition;
            
        return SqlQueryOptimizer.buildBitmapAggEquivalent(
            "application.talent_recruitment_process_id", 
            condition, 
            alias
        );
    }

    /**
     * 获取处理器描述信息
     */
    @Override
    public String toString() {
        return String.format("ToClientProcessor{processorType=%s, targetTable=%s}", 
                           getProcessorType(), getTargetTableName());
    }
}