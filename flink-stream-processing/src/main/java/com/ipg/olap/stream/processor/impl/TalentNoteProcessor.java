package com.ipg.olap.stream.processor.impl;

import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TalentNoteProcessor extends AbstractNoteKpiProcessor {

    private static final Logger log = LoggerFactory.getLogger(TalentNoteProcessor.class);

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        String sql = """
            INSERT INTO %s (tenant_id, note_type, note_id, team_id, team_name, team_parent_id, team_level, team_is_leaf,
                user_id, user_name, user_activated, add_date, dt, callNoteNum, personNoteNum, otherNoteNum, emailNoteNum, videoNoteNum, iciNum, noteCount, unique_talent_ids)
            
            SELECT u.tenant_id                                              AS tenant_id,
                    CAST('talent_note' AS STRING)                           as note_type,
                    tn.id                                                   as note_id,
                    pt.parent_team_id                                       AS team_id,
                    CAST(pt.parent_team_name AS STRING)                     AS team_name,
                    pt.parent_team_parent_id                                AS team_parent_id,
                    pt.parent_team_level                                    AS team_level,
                    pt.parent_team_is_leaf                                  AS team_is_leaf,
                    u.id                                                    AS user_id,
                    CONCAT(u.first_name, ' ', u.last_name)                  AS user_name,
                    u.activated                                             AS user_activated,
                    CAST(tn.created_date AS TIMESTAMP(3))                   AS add_date,
                    CAST(DATE_FORMAT(tn.created_date, 'yyyy-MM') AS STRING) AS dt,
                    ARRAY_AGG(CASE WHEN tn.note_type = 0 THEN tn.id END)       callNoteNum,
                    ARRAY_AGG(CASE WHEN tn.note_type = 1 THEN tn.id END)       personNoteNum,
                    ARRAY_AGG(CASE WHEN tn.note_type = 2 THEN tn.id END)       otherNoteNum,
                    ARRAY_AGG(CASE WHEN tn.note_type = 3 THEN tn.id END)       emailNoteNum,
                    ARRAY_AGG(CASE WHEN tn.note_type = 4 THEN tn.id END)       videoNoteNum,
                    ARRAY_AGG(CASE WHEN tn.note_type = 5 THEN tn.id END)       iciNum,
                    ARRAY_AGG(CASE WHEN tn.agency_id IS NULL THEN tn.id END)   noteCount,
                    ARRAY_AGG(tn.talent_id)                                 AS unique_talent_ids
             FROM (
                SELECT *, PROCTIME() AS proc_time FROM ods_apn.talent_note
             ) AS tn
              LEFT join ods_apn.`user` FOR SYSTEM_TIME AS OF tn.proc_time AS u on u.id = tn.puser_id
              LEFT join ods_apn.permission_user_team FOR SYSTEM_TIME AS OF tn.proc_time AS put on put.user_id = tn.puser_id and put.is_primary = 1
              LEFT join (%s) AS pt on pt.child_team_id = put.team_id
             GROUP BY u.tenant_id, tn.id, pt.parent_team_id,
                      pt.parent_team_name,
                      pt.parent_team_parent_id,
                      pt.parent_team_level,
                      pt.parent_team_is_leaf, u.id, u.first_name, u.last_name, u.activated, tn.created_date
            """.formatted(targetTableName, teamSql());
        log.info("Executing SQL: {}", sql);
        statementSet.addInsertSql(sql);
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "talent_note",
            "talent",
            "user",
            "permission_user_team"
        };
    }
}
