package com.ipg.olap.stream.optimizer;

import java.util.List;

/**
 * SQL 查询优化工具类
 * 提供 SQL 查询优化方法，包括时态关联、UNION ALL 优化、BITMAP_AGG 等效操作
 */
public class SqlQueryOptimizer {

    /**
     * 优化时态 JOIN 查询 - 使用 FOR SYSTEM_TIME AS OF
     * 
     * @param mainTable 主表名
     * @param lookupTable 查找表名
     * @param joinCondition 关联条件
     * @return 优化后的时态关联 SQL
     */
    public static String buildOptimizedTemporalJoin(String mainTable, String lookupTable, String joinCondition) {
        return String.format("""
            SELECT m.*, l.*
            FROM (
                SELECT *, PROCTIME() AS proc_time
                FROM %s
            ) AS m
            LEFT JOIN %s FOR SYSTEM_TIME AS OF m.proc_time AS l
                ON %s
            """, mainTable, lookupTable, joinCondition);
    }

    /**
     * 优化时态 JOIN 查询 - 带字段选择
     * 
     * @param mainTable 主表名
     * @param lookupTable 查找表名
     * @param joinCondition 关联条件
     * @param selectFields 选择的字段
     * @return 优化后的时态关联 SQL
     */
    public static String buildOptimizedTemporalJoin(String mainTable, String lookupTable, 
                                                   String joinCondition, String selectFields) {
        return String.format("""
            SELECT %s
            FROM (
                SELECT *, PROCTIME() AS proc_time
                FROM %s
            ) AS m
            LEFT JOIN %s FOR SYSTEM_TIME AS OF m.proc_time AS l
                ON %s
            """, selectFields, mainTable, lookupTable, joinCondition);
    }

    /**
     * 优化 UNION ALL 查询 - 添加性能提示
     * 
     * @param subQueries 子查询列表
     * @param hints SQL 性能提示
     * @return 优化后的 UNION ALL SQL
     */
    public static String buildOptimizedUnionAll(List<String> subQueries, String hints) {
        if (subQueries == null || subQueries.isEmpty()) {
            throw new IllegalArgumentException("Sub queries cannot be null or empty");
        }
        
        String unionQuery = String.join("\nUNION ALL\n", subQueries);
        return hints + "\n" + unionQuery;
    }

    /**
     * 优化 UNION ALL 查询 - 不带提示
     * 
     * @param subQueries 子查询列表
     * @return 优化后的 UNION ALL SQL
     */
    public static String buildOptimizedUnionAll(List<String> subQueries) {
        if (subQueries == null || subQueries.isEmpty()) {
            throw new IllegalArgumentException("Sub queries cannot be null or empty");
        }
        
        return String.join("\nUNION ALL\n", subQueries);
    }

    /**
     * 创建 BITMAP_AGG 在 Flink SQL 中的等效操作
     * 使用 COLLECT 和 ARRAY 函数实现类似功能
     * 
     * @param field 聚合字段
     * @param condition 聚合条件
     * @return BITMAP_AGG 等效 SQL
     */
    public static String buildBitmapAggEquivalent(String field, String condition) {
        return String.format("""
            COLLECT(CASE WHEN %s THEN %s END) AS %s_bitmap
            """, condition, field, field.toLowerCase().replace(".", "_"));
    }

    /**
     * 创建 BITMAP_AGG 等效操作 - 带别名
     * 
     * @param field 聚合字段
     * @param condition 聚合条件
     * @param alias 别名
     * @return BITMAP_AGG 等效 SQL
     */
    public static String buildBitmapAggEquivalent(String field, String condition, String alias) {
        return String.format("""
            COLLECT(CASE WHEN %s THEN %s END) AS %s
            """, condition, field, alias);
    }

    /**
     * 创建 BITMAP_UNION 等效操作
     * 
     * @param bitmapFields bitmap 字段列表
     * @param alias 结果别名
     * @return BITMAP_UNION 等效 SQL
     */
    public static String buildBitmapUnionEquivalent(List<String> bitmapFields, String alias) {
        if (bitmapFields == null || bitmapFields.isEmpty()) {
            throw new IllegalArgumentException("Bitmap fields cannot be null or empty");
        }
        
        String unionExpression = String.join(" || ", bitmapFields);
        return String.format("(%s) AS %s", unionExpression, alias);
    }

    /**
     * 优化分区查询 - 添加分区裁剪
     * 
     * @param baseQuery 基础查询
     * @param partitionField 分区字段
     * @param partitionValue 分区值
     * @return 优化后的分区查询 SQL
     */
    public static String addPartitionPruning(String baseQuery, String partitionField, String partitionValue) {
        if (baseQuery.toUpperCase().contains("WHERE")) {
            return baseQuery + String.format(" AND %s = '%s'", partitionField, partitionValue);
        } else {
            return baseQuery + String.format(" WHERE %s = '%s'", partitionField, partitionValue);
        }
    }

    /**
     * 优化分区查询 - 添加日期范围分区裁剪
     * 
     * @param baseQuery 基础查询
     * @param partitionField 分区字段
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 优化后的分区查询 SQL
     */
    public static String addDateRangePartitionPruning(String baseQuery, String partitionField, 
                                                     String startDate, String endDate) {
        String condition = String.format("%s >= '%s' AND %s <= '%s'", 
                                        partitionField, startDate, partitionField, endDate);
        
        if (baseQuery.toUpperCase().contains("WHERE")) {
            return baseQuery + " AND " + condition;
        } else {
            return baseQuery + " WHERE " + condition;
        }
    }

    /**
     * 创建优化的 CTE (Common Table Expression)
     * 
     * @param cteName CTE 名称
     * @param cteQuery CTE 查询
     * @param mainQuery 主查询
     * @return 包含 CTE 的完整 SQL
     */
    public static String buildOptimizedCTE(String cteName, String cteQuery, String mainQuery) {
        return String.format("""
            WITH %s AS (
                %s
            )
            %s
            """, cteName, cteQuery, mainQuery);
    }

    /**
     * 创建优化的多个 CTE
     * 
     * @param cteDefinitions CTE 定义映射 (名称 -> 查询)
     * @param mainQuery 主查询
     * @return 包含多个 CTE 的完整 SQL
     */
    public static String buildOptimizedMultipleCTE(java.util.Map<String, String> cteDefinitions, String mainQuery) {
        if (cteDefinitions == null || cteDefinitions.isEmpty()) {
            return mainQuery;
        }
        
        StringBuilder cteBuilder = new StringBuilder("WITH ");
        boolean first = true;
        
        for (java.util.Map.Entry<String, String> entry : cteDefinitions.entrySet()) {
            if (!first) {
                cteBuilder.append(",\n");
            }
            cteBuilder.append(String.format("%s AS (\n    %s\n)", entry.getKey(), entry.getValue()));
            first = false;
        }
        
        return cteBuilder.append("\n").append(mainQuery).toString();
    }

    /**
     * 优化 GROUP BY 查询 - 添加性能提示
     * 
     * @param selectClause SELECT 子句
     * @param fromClause FROM 子句
     * @param groupByClause GROUP BY 子句
     * @param hints 性能提示
     * @return 优化后的 GROUP BY SQL
     */
    public static String buildOptimizedGroupBy(String selectClause, String fromClause, 
                                              String groupByClause, String hints) {
        return String.format("""
            %s
            SELECT %s
            FROM %s
            GROUP BY %s
            """, hints != null ? hints : "", selectClause, fromClause, groupByClause);
    }
}