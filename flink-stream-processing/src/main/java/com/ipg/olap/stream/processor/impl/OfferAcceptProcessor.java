package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.OptimizedSqlProcessor;
import com.ipg.olap.stream.optimizer.SqlQueryOptimizer;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 录用接受阶段处理器
 * 实现 mv_application_offer_accept 录用接受阶段逻辑
 * 
 * 功能包括：
 * - 录用接受阶段的基础指标统计
 * - AI 推荐和精准度跟踪
 * - 当前状态和历史状态的区分处理
 */
public class OfferAcceptProcessor extends OptimizedSqlProcessor {

    private static final Logger log = LoggerFactory.getLogger(OfferAcceptProcessor.class);

    public OfferAcceptProcessor() {
        super("OfferAcceptProcessor", "dwd_application_offer_accept");
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.AGGREGATION;
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "dwd_apn.dwd_application_wide"
        };
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        try {
            log.info("开始处理录用接受阶段数据 - {}", getPerformanceInfo());

            // 构建并执行录用接受处理 SQL
            String offerAcceptSql = buildOfferAcceptProcessingSql();
            log.info("执行录用接受处理 SQL: {}", offerAcceptSql);

            statementSet.addInsertSql(offerAcceptSql);

            log.info("录用接受阶段数据处理完成");

        } catch (Exception e) {
            log.error("录用接受阶段数据处理失败", e);
            throw new RuntimeException("OfferAcceptProcessor 处理失败", e);
        }
    }

    /**
     * 创建录用接受阶段目标表
     */
    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableSql = """
            CREATE TABLE IF NOT EXISTS `dwd_apn`.`dwd_application_offer_accept` (
                tenant_id BIGINT,
                company_id BIGINT,
                job_id BIGINT,
                job_pteam_id BIGINT,
                team_id BIGINT,
                team_name STRING,
                user_id BIGINT,
                user_name STRING,
                user_activated BOOLEAN,
                add_date TIMESTAMP(3),
                event_date TIMESTAMP(3),
                user_roles MULTISET<INT>,
                
                -- 录用接受基础指标
                offer_accept_count_num MULTISET<BIGINT>,
                offer_accept_ai_recommend_count_num MULTISET<BIGINT>,
                offer_accept_precision_ai_recommend_num MULTISET<BIGINT>,
                
                -- 当前录用接受指标
                offer_accept_current_count_num MULTISET<BIGINT>,
                offer_accept_current_ai_recommend_num MULTISET<BIGINT>,
                offer_accept_current_precision_ai_recommend_num MULTISET<BIGINT>,
                
                dt STRING,
                PRIMARY KEY (tenant_id, company_id, job_id, team_id, user_id, add_date, event_date, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )
            """.formatted(getStandardPaimonConfig());

        tableEnv.executeSql(createTableSql);
        log.info("录用接受阶段目标表创建完成");
    }

    /**
     * 获取标准的 Paimon 表配置
     */
    private String getStandardPaimonConfig() {
        return String.join(",\n                ",
            "'write-buffer-size' = '512mb'",
            "'sink.parallelism' = '4'", 
            "'lookup.cache.ttl' = '1h'",
            "'compaction.min.file-num' = '5'",
            "'changelog-producer' = 'lookup'",
            "'precommit-compact' = 'true'",
            "'compaction.max.file-num' = '50'",
            "'snapshot.time-retained' = '1h'"
        );
    }

    /**
     * 构建录用接受处理 SQL
     */
    private String buildOfferAcceptProcessingSql() {
        return String.format("""
            INSERT INTO dwd_apn.dwd_application_offer_accept
            SELECT 
                tenant_id,
                company_id,
                job_id,
                job_pteam_id,
                team_id,
                team_name,
                user_id,
                user_name,
                user_activated,
                add_date,
                event_date,
                %s,
                
                -- 录用接受基础指标
                %s,
                %s,
                %s,
                
                -- 当前录用接受指标
                %s,
                %s,
                %s,
                
                dt
            FROM dwd_apn.dwd_application_wide AS application
            WHERE node_type = 41
            GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, 
                     user_id, user_name, user_activated, add_date, event_date, dt
            """,
            // 用户角色聚合
            SqlQueryOptimizer.buildBitmapAggEquivalent("user_role", "TRUE", "user_roles"),
            
            // 录用接受基础指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.node_id", "TRUE", "offer_accept_count_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "ai_score IS NOT NULL", "offer_accept_ai_recommend_count_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "recommend_feedback_id IS NOT NULL", "offer_accept_precision_ai_recommend_num"),
            
            // 当前录用接受指标
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.node_id", 
                "application.node_status = 1", "offer_accept_current_count_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "ai_score IS NOT NULL AND application.node_status = 1", "offer_accept_current_ai_recommend_num"),
            SqlQueryOptimizer.buildBitmapAggEquivalent("application.talent_recruitment_process_id", 
                "recommend_feedback_id IS NOT NULL AND application.node_status = 1", 
                "offer_accept_current_precision_ai_recommend_num")
        );
    }

    /**
     * 获取处理器描述信息
     */
    @Override
    public String toString() {
        return String.format("OfferAcceptProcessor{processorType=%s, targetTable=%s}", 
                           getProcessorType(), getTargetTableName());
    }
}