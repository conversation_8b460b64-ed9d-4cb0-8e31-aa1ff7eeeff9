package com.ipg.olap.stream.monitor;

import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 性能监控器
 * 
 * 提供详细的性能监控和调试信息，包括：
 * 1. 处理器执行时间监控
 * 2. 内存使用情况监控
 * 3. 算子性能分析
 * 4. 配置优化建议
 */
public class PerformanceMonitor {

    private static final Logger log = LoggerFactory.getLogger(PerformanceMonitor.class);
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    // 性能指标存储
    private static final Map<String, Long> processorStartTimes = new ConcurrentHashMap<>();
    private static final Map<String, Long> processorExecutionTimes = new ConcurrentHashMap<>();
    private static final Map<String, Integer> processorExecutionCounts = new ConcurrentHashMap<>();
    
    /**
     * 开始监控处理器执行
     * 
     * @param processorName 处理器名称
     */
    public static void startProcessorMonitoring(String processorName) {
        long startTime = System.currentTimeMillis();
        processorStartTimes.put(processorName, startTime);
        
        log.info("🚀 [{}] Starting processor: {} at {}", 
            getCurrentTimestamp(), processorName, TIMESTAMP_FORMAT.format(LocalDateTime.now()));
        
        // 记录系统资源状态
        logSystemResources(processorName + "_start");
    }

    /**
     * 结束监控处理器执行
     * 
     * @param processorName 处理器名称
     */
    public static void endProcessorMonitoring(String processorName) {
        Long startTime = processorStartTimes.remove(processorName);
        if (startTime != null) {
            long executionTime = System.currentTimeMillis() - startTime;
            processorExecutionTimes.put(processorName, executionTime);
            processorExecutionCounts.merge(processorName, 1, Integer::sum);
            
            log.info("✅ [{}] Completed processor: {} in {}ms", 
                getCurrentTimestamp(), processorName, executionTime);
            
            // 记录性能分析
            analyzeProcessorPerformance(processorName, executionTime);
            
            // 记录系统资源状态
            logSystemResources(processorName + "_end");
        }
    }

    /**
     * 分析处理器性能
     * 
     * @param processorName 处理器名称
     * @param executionTime 执行时间
     */
    private static void analyzeProcessorPerformance(String processorName, long executionTime) {
        // 性能等级分析
        String performanceLevel;
        String recommendation = "";
        
        if (executionTime < 5000) {
            performanceLevel = "🟢 EXCELLENT";
        } else if (executionTime < 15000) {
            performanceLevel = "🟡 GOOD";
        } else if (executionTime < 30000) {
            performanceLevel = "🟠 MODERATE";
            recommendation = "考虑优化 SQL 查询或增加并行度";
        } else {
            performanceLevel = "🔴 SLOW";
            recommendation = "需要优化：检查 SQL 复杂度、并行度配置和资源分配";
        }
        
        log.info("📊 Performance Analysis - {}: {} ({}ms) - {}", 
            processorName, performanceLevel, executionTime, recommendation);
    }

    /**
     * 记录系统资源使用情况
     * 
     * @param context 上下文信息
     */
    private static void logSystemResources(String context) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
        
        log.info("💾 [{}] Memory Usage: {:.1f}% ({} MB used / {} MB max)", 
            context, memoryUsagePercent, usedMemory / 1024 / 1024, maxMemory / 1024 / 1024);
        
        // 内存使用警告
        if (memoryUsagePercent > 80) {
            log.warn("⚠️ High memory usage detected: {:.1f}% - Consider increasing heap size", memoryUsagePercent);
        }
    }

    /**
     * 监控 TableEnvironment 配置
     * 
     * @param tableEnv Flink TableEnvironment
     * @param layer 层级名称
     */
    public static void monitorTableEnvironmentConfig(StreamTableEnvironment tableEnv, String layer) {
        log.info("🔧 [{}] TableEnvironment Configuration for layer: {}", getCurrentTimestamp(), layer);
        
        // 记录关键配置
        var config = tableEnv.getConfig().getConfiguration();
        
        logConfigValue(config, "table.exec.mini-batch.enabled", "Mini-batch enabled");
        logConfigValue(config, "table.exec.mini-batch.allow-latency", "Mini-batch latency");
        logConfigValue(config, "table.exec.mini-batch.size", "Mini-batch size");
        logConfigValue(config, "table.optimizer.join-reorder-enabled", "Join reorder enabled");
        logConfigValue(config, "table.exec.state.ttl", "State TTL");
        
        log.info("📋 Configuration monitoring completed for layer: {}", layer);
    }

    /**
     * 记录配置值
     */
    private static void logConfigValue(org.apache.flink.configuration.Configuration config, String key, String description) {
        try {
            String value = config.getString(key, "NOT_SET");
            log.info("  📌 {}: {}", description, value);
        } catch (Exception e) {
            log.debug("Could not read config value for key: {}", key);
        }
    }

    /**
     * 生成性能报告
     * 
     * @return 性能报告字符串
     */
    public static String generatePerformanceReport() {
        StringBuilder report = new StringBuilder();
        report.append("\n").append("=" .repeat(60)).append("\n");
        report.append("📊 PERFORMANCE REPORT - ").append(getCurrentTimestamp()).append("\n");
        report.append("=" .repeat(60)).append("\n");
        
        if (processorExecutionTimes.isEmpty()) {
            report.append("No performance data available.\n");
            return report.toString();
        }
        
        // 按执行时间排序
        processorExecutionTimes.entrySet().stream()
            .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
            .forEach(entry -> {
                String processor = entry.getKey();
                long time = entry.getValue();
                int count = processorExecutionCounts.getOrDefault(processor, 1);
                
                report.append(String.format("🔹 %-30s: %6dms (executed %d times)\n", 
                    processor, time, count));
            });
        
        // 总体统计
        long totalTime = processorExecutionTimes.values().stream().mapToLong(Long::longValue).sum();
        int totalExecutions = processorExecutionCounts.values().stream().mapToInt(Integer::intValue).sum();
        
        report.append("\n📈 SUMMARY:\n");
        report.append(String.format("   Total execution time: %dms\n", totalTime));
        report.append(String.format("   Total processors executed: %d\n", totalExecutions));
        report.append(String.format("   Average execution time: %.1fms\n", 
            totalExecutions > 0 ? (double) totalTime / totalExecutions : 0));
        
        report.append("=" .repeat(60)).append("\n");
        
        return report.toString();
    }

    /**
     * 监控作业启动过程
     * 
     * @param jobName 作业名称
     * @param layer 层级
     */
    public static void monitorJobStartup(String jobName, String layer) {
        log.info("🚀 [{}] Starting job: {} (layer: {})", getCurrentTimestamp(), jobName, layer);
        logSystemResources("job_startup_" + layer);
        
        // 记录启动时间
        processorStartTimes.put("JOB_" + jobName, System.currentTimeMillis());
    }

    /**
     * 监控作业完成
     * 
     * @param jobName 作业名称
     * @param layer 层级
     */
    public static void monitorJobCompletion(String jobName, String layer) {
        Long startTime = processorStartTimes.remove("JOB_" + jobName);
        if (startTime != null) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.info("✅ [{}] Job completed: {} (layer: {}) in {}ms", 
                getCurrentTimestamp(), jobName, layer, totalTime);
        }
        
        logSystemResources("job_completion_" + layer);
        
        // 生成并输出性能报告
        String report = generatePerformanceReport();
        log.info(report);
    }

    /**
     * 获取当前时间戳
     */
    private static String getCurrentTimestamp() {
        return TIMESTAMP_FORMAT.format(LocalDateTime.now());
    }

    /**
     * 清理性能数据
     */
    public static void clearPerformanceData() {
        processorStartTimes.clear();
        processorExecutionTimes.clear();
        processorExecutionCounts.clear();
        log.info("🧹 Performance monitoring data cleared");
    }

    /**
     * 获取性能指标
     * 
     * @return 性能指标映射
     */
    public static Map<String, Object> getPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("executionTimes", new HashMap<>(processorExecutionTimes));
        metrics.put("executionCounts", new HashMap<>(processorExecutionCounts));
        metrics.put("timestamp", getCurrentTimestamp());
        
        // 计算统计信息
        if (!processorExecutionTimes.isEmpty()) {
            long totalTime = processorExecutionTimes.values().stream().mapToLong(Long::longValue).sum();
            long maxTime = processorExecutionTimes.values().stream().mapToLong(Long::longValue).max().orElse(0);
            long minTime = processorExecutionTimes.values().stream().mapToLong(Long::longValue).min().orElse(0);
            double avgTime = (double) totalTime / processorExecutionTimes.size();
            
            metrics.put("totalExecutionTime", totalTime);
            metrics.put("maxExecutionTime", maxTime);
            metrics.put("minExecutionTime", minTime);
            metrics.put("avgExecutionTime", avgTime);
        }
        
        return metrics;
    }
}
