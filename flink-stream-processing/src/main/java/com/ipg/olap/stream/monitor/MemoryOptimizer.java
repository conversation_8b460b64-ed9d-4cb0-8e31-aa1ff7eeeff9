package com.ipg.olap.stream.monitor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 内存优化器
 * 
 * 专门针对资源有限环境的内存管理：
 * 1. 实时监控内存使用情况
 * 2. 在内存压力大时触发 GC
 * 3. 提供内存使用建议
 * 4. 防止 OOM 的预警机制
 */
public class MemoryOptimizer {

    private static final Logger log = LoggerFactory.getLogger(MemoryOptimizer.class);
    
    private static final double HIGH_MEMORY_THRESHOLD = 0.8;  // 80% 内存使用率警告
    private static final double CRITICAL_MEMORY_THRESHOLD = 0.9;  // 90% 内存使用率危险
    
    private static ScheduledExecutorService scheduler;
    private static boolean monitoringEnabled = false;
    
    /**
     * 启动内存监控
     * 
     * @param intervalSeconds 监控间隔（秒）
     */
    public static void startMemoryMonitoring(int intervalSeconds) {
        if (monitoringEnabled) {
            log.warn("Memory monitoring is already enabled");
            return;
        }
        
        scheduler = Executors.newScheduledThreadPool(1);
        scheduler.scheduleAtFixedRate(
            MemoryOptimizer::checkMemoryUsage,
            0,
            intervalSeconds,
            TimeUnit.SECONDS
        );
        
        monitoringEnabled = true;
        log.info("🔍 Memory monitoring started with interval: {}s", intervalSeconds);
    }

    /**
     * 停止内存监控
     */
    public static void stopMemoryMonitoring() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            monitoringEnabled = false;
            log.info("🛑 Memory monitoring stopped");
        }
    }

    /**
     * 检查内存使用情况
     */
    private static void checkMemoryUsage() {
        try {
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
            MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
            
            long heapUsed = heapUsage.getUsed();
            long heapMax = heapUsage.getMax();
            double heapUsagePercent = (double) heapUsed / heapMax;
            
            long nonHeapUsed = nonHeapUsage.getUsed();
            long nonHeapMax = nonHeapUsage.getMax();
            double nonHeapUsagePercent = nonHeapMax > 0 ? (double) nonHeapUsed / nonHeapMax : 0;
            
            // 记录内存使用情况
            if (heapUsagePercent > CRITICAL_MEMORY_THRESHOLD) {
                log.error("🚨 CRITICAL: Heap memory usage: {:.1f}% ({} MB / {} MB)", 
                    heapUsagePercent * 100, heapUsed / 1024 / 1024, heapMax / 1024 / 1024);
                
                // 触发紧急 GC
                triggerEmergencyGC();
                
            } else if (heapUsagePercent > HIGH_MEMORY_THRESHOLD) {
                log.warn("⚠️ HIGH: Heap memory usage: {:.1f}% ({} MB / {} MB)", 
                    heapUsagePercent * 100, heapUsed / 1024 / 1024, heapMax / 1024 / 1024);
                
                // 触发建议性 GC
                suggestGC();
                
            } else {
                log.debug("💚 NORMAL: Heap memory usage: {:.1f}% ({} MB / {} MB)", 
                    heapUsagePercent * 100, heapUsed / 1024 / 1024, heapMax / 1024 / 1024);
            }
            
            // 记录非堆内存使用情况
            if (nonHeapMax > 0) {
                log.debug("📊 Non-heap memory usage: {:.1f}% ({} MB / {} MB)", 
                    nonHeapUsagePercent * 100, nonHeapUsed / 1024 / 1024, nonHeapMax / 1024 / 1024);
            }
            
        } catch (Exception e) {
            log.error("Error checking memory usage", e);
        }
    }

    /**
     * 触发紧急垃圾回收
     */
    private static void triggerEmergencyGC() {
        log.warn("🚨 Triggering emergency garbage collection...");
        long beforeGC = getUsedMemory();
        
        System.gc();
        
        // 等待 GC 完成
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long afterGC = getUsedMemory();
        long freedMemory = beforeGC - afterGC;
        
        log.info("🧹 Emergency GC completed. Freed: {} MB", freedMemory / 1024 / 1024);
    }

    /**
     * 建议性垃圾回收
     */
    private static void suggestGC() {
        log.info("💡 Suggesting garbage collection due to high memory usage...");
        System.gc();
    }

    /**
     * 获取当前使用的内存量
     */
    private static long getUsedMemory() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        return memoryBean.getHeapMemoryUsage().getUsed();
    }

    /**
     * 获取内存使用报告
     */
    public static String getMemoryReport() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        
        StringBuilder report = new StringBuilder();
        report.append("\n").append("=".repeat(50)).append("\n");
        report.append("💾 MEMORY USAGE REPORT\n");
        report.append("=".repeat(50)).append("\n");
        
        // 堆内存信息
        long heapUsed = heapUsage.getUsed();
        long heapMax = heapUsage.getMax();
        long heapCommitted = heapUsage.getCommitted();
        double heapUsagePercent = (double) heapUsed / heapMax * 100;
        
        report.append(String.format("🏠 Heap Memory:\n"));
        report.append(String.format("   Used:      %6d MB (%.1f%%)\n", heapUsed / 1024 / 1024, heapUsagePercent));
        report.append(String.format("   Committed: %6d MB\n", heapCommitted / 1024 / 1024));
        report.append(String.format("   Max:       %6d MB\n", heapMax / 1024 / 1024));
        
        // 非堆内存信息
        long nonHeapUsed = nonHeapUsage.getUsed();
        long nonHeapMax = nonHeapUsage.getMax();
        long nonHeapCommitted = nonHeapUsage.getCommitted();
        
        report.append(String.format("\n📚 Non-Heap Memory:\n"));
        report.append(String.format("   Used:      %6d MB\n", nonHeapUsed / 1024 / 1024));
        report.append(String.format("   Committed: %6d MB\n", nonHeapCommitted / 1024 / 1024));
        if (nonHeapMax > 0) {
            report.append(String.format("   Max:       %6d MB\n", nonHeapMax / 1024 / 1024));
        } else {
            report.append("   Max:       Unlimited\n");
        }
        
        // 内存状态评估
        report.append("\n📊 Memory Status: ");
        if (heapUsagePercent > 90) {
            report.append("🚨 CRITICAL - Risk of OOM");
        } else if (heapUsagePercent > 80) {
            report.append("⚠️ HIGH - Monitor closely");
        } else if (heapUsagePercent > 60) {
            report.append("🟡 MODERATE - Normal usage");
        } else {
            report.append("💚 GOOD - Healthy usage");
        }
        
        report.append("\n").append("=".repeat(50)).append("\n");
        
        return report.toString();
    }

    /**
     * 获取内存优化建议
     */
    public static String getOptimizationSuggestions() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        
        long heapUsed = heapUsage.getUsed();
        long heapMax = heapUsage.getMax();
        double heapUsagePercent = (double) heapUsed / heapMax * 100;
        
        StringBuilder suggestions = new StringBuilder();
        suggestions.append("\n💡 MEMORY OPTIMIZATION SUGGESTIONS:\n");
        
        if (heapUsagePercent > 90) {
            suggestions.append("🚨 URGENT ACTIONS NEEDED:\n");
            suggestions.append("   - Increase heap size: -Xmx parameter\n");
            suggestions.append("   - Reduce parallelism to decrease memory pressure\n");
            suggestions.append("   - Enable incremental checkpointing\n");
            suggestions.append("   - Reduce mini-batch size\n");
        } else if (heapUsagePercent > 80) {
            suggestions.append("⚠️ RECOMMENDED ACTIONS:\n");
            suggestions.append("   - Monitor memory usage closely\n");
            suggestions.append("   - Consider reducing cache sizes\n");
            suggestions.append("   - Optimize SQL queries to reduce state\n");
            suggestions.append("   - Enable state TTL for cleanup\n");
        } else if (heapUsagePercent > 60) {
            suggestions.append("🟡 OPTIONAL OPTIMIZATIONS:\n");
            suggestions.append("   - Current usage is acceptable\n");
            suggestions.append("   - Consider enabling G1GC for better pause times\n");
            suggestions.append("   - Monitor during peak load\n");
        } else {
            suggestions.append("💚 MEMORY USAGE IS OPTIMAL:\n");
            suggestions.append("   - No immediate action needed\n");
            suggestions.append("   - Current configuration is working well\n");
        }
        
        return suggestions.toString();
    }

    /**
     * 执行内存清理
     */
    public static void performMemoryCleanup() {
        log.info("🧹 Performing memory cleanup...");
        
        long beforeCleanup = getUsedMemory();
        
        // 强制垃圾回收
        System.gc();
        
        // 等待 GC 完成
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long afterCleanup = getUsedMemory();
        long freedMemory = beforeCleanup - afterCleanup;
        
        log.info("✅ Memory cleanup completed. Freed: {} MB", freedMemory / 1024 / 1024);
    }
}
