# Implementation Plan

- [x] 1. Flink SQL 基础优化框架

  - 创建 FlinkSqlOptimizer 类优化 TableEnvironment 配置
  - 实现 OptimizedSqlProcessor 基类支持不同处理器类型的优化配置
  - 开发 SqlQueryOptimizer 工具类提供 SQL 查询优化方法
  - _Requirements: 1.1, 1.2, 1.3, 2.1, 2.2, 5.1, 5.2_

- [x] 1.1 创建 FlinkSqlOptimizer 类优化 SQL 执行环境

  - 实现 TableEnvironment 的 SQL 执行优化参数配置，包括 join 重排序和广播阈值
  - 添加 mini-batch 优化配置提高吞吐量并减少状态访问频率
  - 配置状态 TTL 和 sink 优化参数防止不必要的物化操作
  - _Requirements: 1.1, 1.2, 1.3, 2.1, 2.2_

- [x] 1.2 创建 OptimizedSqlProcessor 基类扩展 AbstractTableProcessor

  - 实现 ProcessorType 枚举定义不同处理阶段的推荐并行度配置
  - 添加 getDefaultPaimonTableConfig 方法根据处理器类型优化写缓冲和缓存 TTL
  - 创建 getOptimizedSqlHints 方法为不同处理器类型生成 SQL 性能提示
  - _Requirements: 1.4, 2.3, 5.1, 5.2_

- [x] 1.3 创建 SqlQueryOptimizer 工具类提供 SQL 查询优化方法

  - 实现 buildOptimizedTemporalJoin 方法优化 FOR SYSTEM_TIME AS OF 时态关联查询
  - 添加 buildOptimizedUnionAll 方法优化 UNION ALL 操作的性能
  - 创建 buildBitmapAggEquivalent 方法实现 BITMAP_AGG 在 Flink SQL 中的等效操作
  - _Requirements: 7.1, 7.2, 7.3_

- [x] 2. 优化现有维度和事实表处理器

  - 重构 ApplicationDimensionProcessor 使用优化的 SQL 配置和时态关联
  - 增强 ApplicationFactProcessor 实现完整的 UNION ALL 逻辑覆盖所有节点类型
  - 创建 ApplicationWideProcessor 实现 mv_application_wide 的复杂关联逻辑
  - _Requirements: 4.1, 7.1, 7.2, 7.3_

- [x] 2.1 优化 ApplicationDimensionProcessor 使用 Flink SQL 最佳实践

  - 实现优化的时态关联查询替换现有的 LEFT JOIN 逻辑
  - 添加 lookup 缓存配置优化 company、job、user 表的查询性能
  - 使用 SqlQueryOptimizer 工具类生成优化的 SQL 查询语句
  - _Requirements: 4.1, 7.1, 7.2_

- [x] 2.2 增强 ApplicationFactProcessor 实现完整的事实表 UNION ALL 逻辑

  - 添加缺失的 interview、offer、offer_accept、onboard、eliminate 节点类型处理
  - 优化所有 UNION ALL 子查询使用 FOR SYSTEM_TIME AS OF 进行时态关联
  - 实现高效的分区策略和 S3 写入性能优化配置
  - _Requirements: 4.1, 7.1, 7.3_

- [x] 2.3 创建 ApplicationWideProcessor 实现 mv_application_wide 宽表逻辑

  - 实现复杂的 CTE 逻辑包括 fact_with_user 子查询处理 KPI 用户关系
  - 优化事实表和维度表的关联以及用户团队信息的连接
  - 添加高效的状态管理配置支持大宽表的分区和缓存策略
  - _Requirements: 4.1, 4.2, 7.1, 7.4_

- [x] 2.4 修复数据库上下文管理问题

  - 修复 PaimonConfig 中的数据库上下文设置，确保处理器能正确访问不同数据库的表
  - 更新 AbstractTableProcessor 的表验证逻辑支持跨数据库表名解析
  - 在所有 SQL 查询中使用完全限定的表名（database.table）避免上下文依赖
  - _Requirements: 1.1, 4.1_

- [x] 2.5 修复 Flink SQL 语法兼容性问题

  - 修复 CTE + INSERT INTO 语法问题，将 INSERT 语句移到最外层
  - 修复时间戳算术运算，使用 TIMESTAMPDIFF 替代直接减法操作
  - 修复表模式类型匹配问题，将所有处理器的 ARRAY 类型改为 MULTISET 类型匹配 COLLECT 函数输出
  - 批量修复了 19 个处理器文件中的类型定义，包括 user_roles 字段的 INT 类型精度修正
  - 确保所有 SQL 查询符合 Flink SQL 语法规范
  - _Requirements: 1.1, 1.2_

- [x] 3. 实现团队层级和 KPI 处理器

  - 创建 TeamHierarchyProcessor 实现 mv_team_hierarchy 物化视图逻辑
  - 实现 CreatedKpiProcessor 处理 mv_created_kpi 的公司、职位、人才创建指标
  - 开发 NoteKpiProcessor 处理 mv_note_kpi 的人才笔记和跟踪指标
  - _Requirements: 4.2, 4.3_

- [x] 3.1 创建 TeamHierarchyProcessor 实现团队层级计算

  - 实现团队层级关系计算逻辑包括父子关系的 LIKE 匹配优化
  - 优化基于字符串的层级匹配操作使用高效的字符串函数
  - 添加团队层级数据的适当缓存配置避免重复计算
  - _Requirements: 4.3, 7.1_

- [x] 3.2 实现 CreatedKpiProcessor 处理创建类 KPI 指标

  - 创建复杂的 UNION ALL 逻辑处理公司创建、升级、职位创建、人才创建
  - 实现 BITMAP_AGG 等效聚合操作使用 Flink SQL 的 COLLECT 和 ARRAY 函数
  - 优化团队层级关联确保 KPI 在各团队层级的正确归属
  - _Requirements: 4.2, 4.3_

- [x] 3.3 开发 NoteKpiProcessor 处理笔记类 KPI 指标

  - 实现笔记 KPI 逻辑处理 talent_note、application_note、talent_tracking_note
  - 创建高效的 BITMAP_AGG 等效聚合针对不同笔记类型的分类统计
  - 优化笔记、用户、团队层级数据之间的复杂关联查询
  - _Requirements: 4.2, 4.3_

- [x] 4. 实现面试和申请阶段处理器

  - 创建 InterviewProcessor 实现 mv_application_interview 的复杂面试阶段逻辑
  - 实现阶段特定处理器包括 offer、offer_accept、onboard、eliminate 阶段
  - 开发 ToJobProcessor 和 ToClientProcessor 处理提交阶段指标
  - _Requirements: 4.1, 4.2_

- [x] 4.1 创建 InterviewProcessor 实现 mv_application_interview 面试逻辑

  - 实现复杂的 BITMAP_AGG 逻辑处理不同面试阶段（interview1、interview2、final）
  - 添加 AI 推荐跟踪功能包括面试阶段的精准度指标计算
  - 优化当前 vs 历史面试指标的状态管理和计算逻辑
  - _Requirements: 4.1, 4.2_

- [x] 4.2 实现 OfferProcessor 处理 mv_application_offer 录用阶段

  - 创建录用阶段逻辑包括小于 2 周录用指标的时间计算
  - 实现 AI 推荐和精准度跟踪针对录用阶段的效果分析
  - 添加职位状态过滤和基于日期的录用有效性检查
  - _Requirements: 4.1, 4.2_

- [x] 4.3 创建提交阶段处理器（ToJobProcessor、ToClientProcessor）

  - 实现 mv_application_to_job 逻辑包括滞留指标和基于时间的计算
  - 创建 mv_application_to_client 逻辑包括复杂的滞留计算和职位状态过滤
  - 优化基于时间的计算使用 Flink SQL 的时间函数和适当的窗口操作
  - _Requirements: 4.1, 4.2_

- [x] 5. 实现最终聚合视图处理器

  - 创建 CurrentKpiViewProcessor 实现 view_application_current_kpi 团队层级聚合
  - 实现 FunnelKpiViewProcessor 处理 view_application_funnel_kpi 完整漏斗指标
  - 开发公司特定视图处理器支持公司级别的 KPI 聚合
  - _Requirements: 4.4_

- [x] 5.1 创建 CurrentKpiViewProcessor 实现当前 KPI 视图

  - 实现大规模 UNION ALL 逻辑合并所有阶段的当前 KPI 指标
  - 优化团队层级关联和 BITMAP_UNION 聚合操作的性能
  - 添加适当的分区和状态管理配置支持大型聚合视图
  - _Requirements: 4.4_

- [x] 5.2 实现 FunnelKpiViewProcessor 处理漏斗 KPI 视图

  - 创建完整的漏斗 KPI 逻辑包括所有申请阶段的历史指标
  - 实现高效的 BITMAP_UNION 操作支持跨团队层级的漏斗分析
  - 优化复杂聚合逻辑使用适当的窗口和状态管理策略
  - _Requirements: 4.4_

- [x] 5.3 开发 CompanyKpiViewProcessors 处理公司级 KPI 视图

  - 实现 view_company_application_current_kpi 和 view_company_application_funnel_kpi
  - 创建公司级聚合逻辑简化团队层级复杂性
  - 优化面向公司特定报告和分析用例的查询性能
  - _Requirements: 4.4_

- [ ] 6. 实现高级监控和告警系统

  - 创建全面的指标收集覆盖所有处理器和操作
  - 实现自定义 Flink 指标监控 S3 写入性能、检查点持续时间、缓存命中率
  - 设置告警阈值和通知系统应对性能下降情况
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [ ] 6.1 创建 MetricsConfiguration 和自定义指标收集

  - 实现自定义 Flink 指标包括每秒处理记录数、S3 写入延迟、内存利用率
  - 添加检查点持续时间监控和反压比率跟踪
  - 创建 lookup 缓存命中率指标监控维度表性能
  - _Requirements: 6.1, 6.2_

- [ ] 6.2 实现告警系统集成

  - 设置所有关键性能指标的可配置告警阈值
  - 实现检查点失败和性能下降的通知机制
  - 添加详细的错误日志和诊断信息支持故障排除
  - _Requirements: 6.3, 6.4_

- [ ] 6.3 创建性能监控仪表板

  - 实现实时监控仪表板显示关键性能指标
  - 添加历史性能趋势和容量规划指标
  - 创建自动化性能报告和优化建议
  - _Requirements: 6.1, 6.2_

- [ ] 7. 优化资源管理和扩缩容

  - 实现基于数据量和处理需求的动态并行度调整
  - 创建不同处理阶段和数据量的内存管理策略
  - 添加自动扩缩容功能包括适当的资源分配和释放
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [ ] 7.1 实现动态并行度管理

  - 创建基于吞吐量和延迟指标的自动并行度调整算法
  - 添加不同处理器类型和数据量的资源分配策略
  - 实现扩缩容策略包括适当的资源限制和约束
  - _Requirements: 5.1, 5.2_

- [ ] 7.2 优化内存管理和分配

  - 实现不同处理阶段（维度、事实、聚合）的内存分配策略
  - 添加内存压力检测和自动缓存大小调整
  - 创建溢写到磁盘机制处理大状态和临时数据
  - _Requirements: 5.2, 5.4, 7.4_

- [ ] 7.3 创建 S3 性能优化

  - 实现智能重试机制包括 S3 操作的熔断器模式
  - 添加写入批处理和压缩策略减少 S3 I/O 开销
  - 创建 S3 连接池和并发写入优化
  - _Requirements: 5.3, 2.1, 2.2_

- [ ] 8. 集成测试和验证

  - 创建端到端管道验证的综合测试套件
  - 实现性能基准测试和回归测试
  - 验证数据一致性和正确性对比 StarRocks 物化视图
  - _Requirements: All requirements validation_

- [ ] 8.1 创建端到端集成测试

  - 实现覆盖完整 ODS 到 DWD 到 StarRocks 管道的测试场景
  - 添加数据一致性验证比较 Flink 输出与 StarRocks 物化视图
  - 创建使用真实数据量和模式的性能基准测试
  - _Requirements: All requirements validation_

- [ ] 8.2 实现故障恢复和弹性测试

  - 创建检查点恢复和精确一次处理验证的测试场景
  - 添加 S3 故障、网络问题、资源约束的混沌工程测试
  - 实现模式演进和向后兼容性的自动化测试
  - _Requirements: 3.4, 5.3_

- [ ] 8.3 创建生产部署和监控设置
  - 实现生产就绪配置包括适当的资源分配和扩缩容策略
  - 设置生产环境的综合监控、告警和日志记录
  - 创建部署自动化和回滚程序确保生产更新的安全性
  - _Requirements: 6.1, 6.2, 6.3, 6.4_
