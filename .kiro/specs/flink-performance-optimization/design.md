# Flink SQL API ODS to DWD Performance Optimization Design

## Overview

This design document outlines a comprehensive approach to optimize the Flink SQL streaming job that processes ODS to DWD data transformation. The solution focuses on Flink SQL API optimizations, addresses performance bottlenecks in snapshot reading, writer operations, checkpoint management, and implements the migration of StarRocks materialized view logic to Flink SQL processors.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Paimon ODS (AWS S3)"
        ODS1[talent_recruitment_process]
        ODS2[job]
        ODS3[company]
        ODS4[user]
        ODS5[permission_team]
        ODS6[...]
    end

    subgraph "Flink SQL Processing Pipeline"
        subgraph "Layer 1: Dimension Processing"
            DIM1[ApplicationDimensionProcessor]
            DIM2[TeamHierarchyProcessor]
        end

        subgraph "Layer 2: Fact Processing"
            FACT1[ApplicationFactProcessor]
        end

        subgraph "Layer 3: Wide Table Processing"
            WIDE1[ApplicationWideProcessor]
        end

        subgraph "Layer 4: Stage Processing"
            STAGE1[InterviewProcessor]
            STAGE2[OfferProcessor]
            STAGE3[ToJobProcessor]
            STAGE4[ToClientProcessor]
        end

        subgraph "Layer 5: KPI Processing"
            KPI1[CreatedKpiProcessor]
            KPI2[NoteKpiProcessor]
        end

        subgraph "Layer 6: Final Views"
            VIEW1[CurrentKpiViewProcessor]
            VIEW2[FunnelKpiViewProcessor]
        end
    end

    subgraph "Paimon DWD (AWS S3)"
        DWD1[dwd_application_dimension]
        DWD2[dwd_application_fact]
        DWD3[dwd_application_wide]
        DWD4[dwd_application_kpi]
    end

    subgraph "StarRocks Query Layer"
        VIEW_FINAL1[view_application_current_kpi]
        VIEW_FINAL2[view_application_funnel_kpi]
    end

    ODS1 --> DIM1
    ODS2 --> FACT1
    DIM1 --> WIDE1
    FACT1 --> WIDE1
    WIDE1 --> STAGE1
    STAGE1 --> KPI1
    KPI1 --> VIEW1
    VIEW1 --> VIEW_FINAL1
```

### Processing Pipeline Design

The processing pipeline follows a layered approach optimized for Flink SQL:

1. **Dimension Layer**: Process small, frequently-accessed reference data
2. **Fact Layer**: Process large volume transactional data with UNION ALL operations
3. **Wide Layer**: Join dimensions and facts for comprehensive data model
4. **Stage Layer**: Process specific business stages (interview, offer, etc.)
5. **KPI Layer**: Calculate business metrics and aggregations
6. **View Layer**: Generate final analytical views for StarRocks

## Components and Interfaces

### 1. Flink SQL 执行环境优化

```java
public class FlinkSqlOptimizer {

    /**
     * 配置TableEnvironment的SQL执行优化参数
     */
    public static void optimizeTableEnvironment(StreamTableEnvironment tableEnv) {
        Configuration config = tableEnv.getConfig().getConfiguration();

        // Join优化 - 提高多表关联性能
        config.setString("table.optimizer.join-reorder-enabled", "true");
        config.setString("table.optimizer.join.broadcast-threshold", "10MB");

        // Mini-batch优化 - 提高吞吐量，减少状态访问
        config.setString("table.exec.mini-batch.enabled", "true");
        config.setString("table.exec.mini-batch.allow-latency", "1s");
        config.setString("table.exec.mini-batch.size", "1000");

        // 状态TTL优化 - 防止状态无限增长
        config.setString("table.exec.state.ttl", "1h");

        // Sink优化 - 减少不必要的物化操作
        config.setString("table.exec.sink.not-null-enforcer", "drop");
        config.setString("table.exec.sink.upsert-materialize", "none");

        // 资源优化
        config.setString("table.exec.resource.default-parallelism", "4");

        // 时间语义优化
        config.setString("table.exec.emit.early-fire.enabled", "true");
        config.setString("table.exec.emit.early-fire.delay", "1s");
    }
}
```

### 2. 优化的 SQL 处理器基类

```java
public abstract class OptimizedSqlProcessor extends AbstractTableProcessor {

    public enum ProcessorType {
        DIMENSION("维度表处理", 2),
        FACT("事实表处理", 4),
        WIDE("宽表处理", 6),
        AGGREGATION("聚合处理", 8),
        KPI("KPI计算", 4);

        private final String description;
        private final int recommendedParallelism;
    }

    /**
     * 根据处理器类型获取优化的Paimon配置
     */
    @Override
    protected Configuration getDefaultPaimonTableConfig() {
        Configuration config = new Configuration();
        ProcessorType type = getProcessorType();

        switch (type) {
            case DIMENSION:
                // 维度表：小数据量，高查询频率
                config.setString("write-buffer-size", "128mb");
                config.setString("sink.parallelism", "2");
                config.setString("lookup.cache.ttl", "24h");
                config.setString("compaction.min.file-num", "2");
                break;

            case FACT:
                // 事实表：中等数据量，批量写入
                config.setString("write-buffer-size", "512mb");
                config.setString("sink.parallelism", "4");
                config.setString("lookup.cache.ttl", "1h");
                config.setString("compaction.min.file-num", "5");
                break;

            case WIDE:
                // 宽表：大数据量，高吞吐
                config.setString("write-buffer-size", "1gb");
                config.setString("sink.parallelism", "8");
                config.setString("lookup.cache.ttl", "30m");
                config.setString("compaction.min.file-num", "10");
                break;

            case AGGREGATION:
                // 聚合表：复杂计算
                config.setString("write-buffer-size", "256mb");
                config.setString("sink.parallelism", "6");
                config.setString("lookup.cache.ttl", "2h");
                config.setString("compaction.min.file-num", "3");
                break;

            case KPI:
                // KPI表：最终输出
                config.setString("write-buffer-size", "256mb");
                config.setString("sink.parallelism", "4");
                config.setString("lookup.cache.ttl", "4h");
                config.setString("compaction.min.file-num", "2");
                break;
        }

        // 通用优化配置
        config.setString("changelog-producer", "lookup");
        config.setString("precommit-compact", "true");
        config.setString("compaction.max.file-num", "50");
        config.setString("snapshot.time-retained", "1h");
        config.setString("scan.snapshot-id", "latest");

        return config;
    }

    protected abstract ProcessorType getProcessorType();

    /**
     * 获取优化的SQL执行提示
     */
    protected String getOptimizedSqlHints() {
        ProcessorType type = getProcessorType();
        return switch (type) {
            case DIMENSION -> "/*+ OPTIONS('sink.parallelism'='2', 'sink.buffer-flush.max-rows'='1000') */";
            case FACT -> "/*+ OPTIONS('sink.parallelism'='4', 'sink.buffer-flush.max-rows'='10000') */";
            case WIDE -> "/*+ OPTIONS('sink.parallelism'='8', 'sink.buffer-flush.max-rows'='50000') */";
            case AGGREGATION -> "/*+ OPTIONS('sink.parallelism'='6', 'sink.buffer-flush.interval'='10s') */";
            case KPI -> "/*+ OPTIONS('sink.parallelism'='4', 'sink.buffer-flush.interval'='5s') */";
        };
    }
}
```

### 3. SQL 查询优化工具类

```java
public class SqlQueryOptimizer {

    /**
     * 优化时态JOIN查询 - 使用FOR SYSTEM_TIME AS OF
     */
    public static String buildOptimizedTemporalJoin(String mainTable, String lookupTable,
                                                   String joinCondition) {
        return String.format("""
            SELECT m.*, l.*
            FROM (
                SELECT *, PROCTIME() AS proc_time
                FROM %s
            ) AS m
            LEFT JOIN %s FOR SYSTEM_TIME AS OF m.proc_time AS l
                ON %s
            """, mainTable, lookupTable, joinCondition);
    }

    /**
     * 优化UNION ALL查询 - 添加性能提示
     */
    public static String buildOptimizedUnionAll(List<String> subQueries, String hints) {
        String unionQuery = String.join("\nUNION ALL\n", subQueries);
        return hints + "\n" + unionQuery;
    }

    /**
     * 优化聚合查询 - 使用BITMAP_AGG等效实现
     */
    public static String buildBitmapAggEquivalent(String field, String condition) {
        return String.format("""
            COLLECT(CASE WHEN %s THEN %s END) AS %s_bitmap
            """, condition, field, field.toLowerCase());
    }

    /**
     * 优化分区查询 - 添加分区裁剪
     */
    public static String addPartitionPruning(String baseQuery, String partitionField, String partitionValue) {
        return baseQuery + String.format(" WHERE %s = '%s'", partitionField, partitionValue);
    }
}
```

## Data Models

### 1. 优化的维度表模型

```sql
-- 优化的应用维度表
CREATE TABLE IF NOT EXISTS dwd_apn.dwd_application_dimension (
    tenant_id BIGINT,
    company_id BIGINT,
    company_name STRING,
    job_id BIGINT,
    job_title STRING,
    job_pteam_id BIGINT,
    talent_recruitment_process_id BIGINT,
    talent_id BIGINT,
    ai_score DOUBLE,
    recommend_feedback_id BIGINT,
    dt STRING,
    PRIMARY KEY (talent_recruitment_process_id, dt) NOT ENFORCED
) PARTITIONED BY (dt)
WITH (
    'write-buffer-size' = '128mb',
    'sink.parallelism' = '2',
    'lookup.cache.ttl' = '24h',
    'changelog-producer' = 'lookup'
);
```

### 2. 优化的事实表模型

```sql
-- 优化的应用事实表
CREATE TABLE IF NOT EXISTS dwd_apn.dwd_application_fact (
    talent_recruitment_process_id BIGINT,
    node_type INT,
    node_status INT,
    node_id BIGINT,
    progress INT,
    final_round BOOLEAN,
    add_date TIMESTAMP(3),
    event_date TIMESTAMP(3),
    last_modified_date TIMESTAMP(3),
    note_last_modify_date TIMESTAMP(3),
    dt STRING,
    operator BIGINT,
    PRIMARY KEY (talent_recruitment_process_id, node_type, node_id, dt) NOT ENFORCED
) PARTITIONED BY (dt)
WITH (
    'write-buffer-size' = '512mb',
    'sink.parallelism' = '4',
    'lookup.cache.ttl' = '1h',
    'changelog-producer' = 'lookup',
    'compaction.min.file-num' = '5'
);
```

### 3. 优化的宽表模型

```sql
-- 优化的应用宽表
CREATE TABLE IF NOT EXISTS dwd_apn.dwd_application_wide (
    tenant_id BIGINT,
    company_id BIGINT,
    job_id BIGINT,
    job_pteam_id BIGINT,
    talent_recruitment_process_id BIGINT,
    talent_id BIGINT,
    ai_score DOUBLE,
    recommend_feedback_id BIGINT,
    team_id BIGINT,
    team_name STRING,
    user_id BIGINT,
    user_name STRING,
    user_activated BOOLEAN,
    user_role INT,
    node_id BIGINT,
    node_type INT,
    node_status INT,
    progress INT,
    final_round BOOLEAN,
    add_date TIMESTAMP(3),
    event_date TIMESTAMP(3),
    last_modified_date TIMESTAMP(3),
    note_last_modify_date TIMESTAMP(3),
    dt STRING,
    PRIMARY KEY (talent_recruitment_process_id, user_id, node_id, dt) NOT ENFORCED
) PARTITIONED BY (dt)
WITH (
    'write-buffer-size' = '1gb',
    'sink.parallelism' = '8',
    'lookup.cache.ttl' = '30m',
    'changelog-producer' = 'lookup',
    'compaction.min.file-num' = '10'
);
```

## Error Handling

### 1. SQL 执行错误处理

```java
public class SqlErrorHandler {

    /**
     * 处理SQL执行异常
     */
    public static void handleSqlException(Exception e, String processorName, String sql) {
        log.error("SQL execution failed in processor: {}", processorName, e);
        log.error("Failed SQL: {}", sql);

        // 根据异常类型进行不同处理
        if (e instanceof TableException) {
            handleTableException((TableException) e, processorName);
        } else if (e instanceof ValidationException) {
            handleValidationException((ValidationException) e, processorName);
        }
    }

    /**
     * 处理表相关异常
     */
    private static void handleTableException(TableException e, String processorName) {
        // 记录表操作异常，可能需要重建表或调整配置
        log.warn("Table operation failed in {}, consider table recreation", processorName);
    }

    /**
     * 处理SQL验证异常
     */
    private static void handleValidationException(ValidationException e, String processorName) {
        // 记录SQL语法或语义错误
        log.error("SQL validation failed in {}, check SQL syntax", processorName);
    }
}
```

### 2. 资源管理和重试策略

```java
public class ResourceManager {

    /**
     * 检查资源使用情况并调整配置
     */
    public static void adjustResourceConfiguration(StreamTableEnvironment tableEnv,
                                                 double memoryUsage, double cpuUsage) {
        Configuration config = tableEnv.getConfig().getConfiguration();

        if (memoryUsage > 0.8) {
            // 内存使用率过高，减少并行度
            config.setString("table.exec.resource.default-parallelism", "2");
            config.setString("table.exec.mini-batch.size", "500");
        } else if (memoryUsage < 0.5 && cpuUsage < 0.5) {
            // 资源充足，可以增加并行度
            config.setString("table.exec.resource.default-parallelism", "8");
            config.setString("table.exec.mini-batch.size", "2000");
        }
    }
}
```

## Testing Strategy

### 1. SQL 性能测试

```java
public class SqlPerformanceTest {

    /**
     * 测试SQL查询性能
     */
    @Test
    public void testQueryPerformance() {
        // 测试不同并行度下的查询性能
        for (int parallelism : Arrays.asList(2, 4, 8, 16)) {
            long startTime = System.currentTimeMillis();

            // 执行测试查询
            executeTestQuery(parallelism);

            long duration = System.currentTimeMillis() - startTime;
            log.info("Parallelism: {}, Duration: {}ms", parallelism, duration);
        }
    }

    /**
     * 测试内存使用情况
     */
    @Test
    public void testMemoryUsage() {
        // 监控不同配置下的内存使用
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();

        long beforeMemory = memoryBean.getHeapMemoryUsage().getUsed();
        executeTestQuery(4);
        long afterMemory = memoryBean.getHeapMemoryUsage().getUsed();

        log.info("Memory used: {} MB", (afterMemory - beforeMemory) / 1024 / 1024);
    }
}
```

### 2. 数据一致性测试

```java
public class DataConsistencyTest {

    /**
     * 验证Flink SQL输出与StarRocks物化视图的一致性
     */
    @Test
    public void testDataConsistency() {
        // 比较Flink输出和StarRocks物化视图的结果
        List<Row> flinkResults = executeFlinkQuery();
        List<Row> starrocksResults = executeStarrocksQuery();

        assertEquals(flinkResults.size(), starrocksResults.size());

        // 逐行比较数据
        for (int i = 0; i < flinkResults.size(); i++) {
            assertRowEquals(flinkResults.get(i), starrocksResults.get(i));
        }
    }
}
```

## Implementation Phases

### Phase 1: 基础优化 (Weeks 1-2)

- 实现 FlinkSqlOptimizer 和 OptimizedSqlProcessor 基类
- 优化现有的 ApplicationDimensionProcessor 和 ApplicationFactProcessor
- 配置 Paimon 表的性能参数

### Phase 2: 事实表完善 (Weeks 3-4)

- 完善 ApplicationFactProcessor 的所有 UNION ALL 逻辑
- 实现 ApplicationWideProcessor
- 优化时态 JOIN 性能

### Phase 3: 阶段处理器 (Weeks 5-6)

- 实现 InterviewProcessor, OfferProcessor 等阶段处理器
- 迁移所有 mv*application*\*物化视图逻辑
- 优化 BITMAP_AGG 等效实现

### Phase 4: KPI 处理器 (Weeks 7-8)

- 实现 CreatedKpiProcessor 和 NoteKpiProcessor
- 实现 TeamHierarchyProcessor
- 优化复杂聚合查询性能

### Phase 5: 最终视图 (Weeks 9-10)

- 实现 CurrentKpiViewProcessor 和 FunnelKpiViewProcessor
- 性能调优和监控集成
- 生产环境部署和验证
